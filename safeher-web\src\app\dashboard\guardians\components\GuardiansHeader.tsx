import { MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';

export default function GuardiansHeader() {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Guardians</h1>
      </div>
      
      <div className="flex items-center space-x-2">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search..."
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          />
        </div>
        
        <Button variant="outline" size="sm" className="flex items-center space-x-2">
          <FunnelIcon className="w-4 h-4" />
          <span>Filter</span>
        </Button>
      </div>
    </div>
  );
}
