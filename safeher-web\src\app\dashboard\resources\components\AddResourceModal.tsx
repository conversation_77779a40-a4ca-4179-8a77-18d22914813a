'use client';

import { useState } from 'react';
import { XMarkIcon, PhotoIcon, LinkIcon } from '@heroicons/react/24/outline';
import { getTypography, createButtonVariant } from '@/lib/design-tokens';

interface AddResourceModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AddResourceModal({ isOpen, onClose }: AddResourceModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    category: '',
    excerpt: '',
    content: '',
    url: '',
    readTime: '',
    tags: '',
  });

  const [selectedImage, setSelectedImage] = useState<File | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Resource submitted:', formData, selectedImage);
    onClose();
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedImage(e.target.files[0]);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border-light">
          <h2 className={`${getTypography('title-b-20')} text-text-primary`}>
            Add New Resource
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-text-light" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Resource Title *
            </label>
            <input
              type="text"
              required
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter a compelling title for your resource"
            />
          </div>

          {/* Category and Read Time */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
                Category *
              </label>
              <select
                required
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">Select category</option>
                <option value="cybersecurity">Cybersecurity</option>
                <option value="digital-safety">Digital Safety</option>
                <option value="online-safety">Online Safety</option>
                <option value="training">Training</option>
                <option value="policy">Policy</option>
                <option value="womens-rights">Women's Rights</option>
                <option value="legal">Legal</option>
                <option value="awareness">Awareness</option>
              </select>
            </div>

            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
                Estimated Read Time
              </label>
              <input
                type="text"
                value={formData.readTime}
                onChange={(e) => setFormData({ ...formData, readTime: e.target.value })}
                className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="e.g., 5 min read"
              />
            </div>
          </div>

          {/* Excerpt */}
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Excerpt/Summary *
            </label>
            <textarea
              required
              rows={3}
              value={formData.excerpt}
              onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Write a brief summary that will appear on the resource card..."
            />
          </div>

          {/* Content */}
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Full Content *
            </label>
            <textarea
              required
              rows={8}
              value={formData.content}
              onChange={(e) => setFormData({ ...formData, content: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Write the full content of your resource article..."
            />
          </div>

          {/* URL */}
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              External URL (Optional)
            </label>
            <div className="relative">
              <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-text-light" />
              <input
                type="url"
                value={formData.url}
                onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                className="w-full pl-10 pr-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="https://example.com/full-article"
              />
            </div>
          </div>

          {/* Featured Image */}
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Featured Image
            </label>
            <div className="border-2 border-dashed border-border-light rounded-lg p-6 text-center">
              <PhotoIcon className="mx-auto h-12 w-12 text-text-light" />
              <div className="mt-4">
                <label htmlFor="image-upload" className="cursor-pointer">
                  <span className={`${getTypography('paragraph-m-12')} text-primary hover:text-primary-shade`}>
                    Upload image
                  </span>
                  <input
                    id="image-upload"
                    name="image-upload"
                    type="file"
                    accept="image/*"
                    className="sr-only"
                    onChange={handleImageChange}
                  />
                </label>
                <p className={`${getTypography('paragraph-r-10')} text-text-light mt-1`}>
                  PNG, JPG up to 5MB
                </p>
              </div>
              {selectedImage && (
                <div className="mt-4">
                  <p className={`${getTypography('paragraph-m-10')} text-text-primary`}>
                    {selectedImage.name}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Tags */}
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Tags (Optional)
            </label>
            <input
              type="text"
              value={formData.tags}
              onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter tags separated by commas (e.g., cybersecurity, women, safety)"
            />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-4 pt-4 border-t border-border-light">
            <button
              type="button"
              onClick={onClose}
              className={`px-6 py-2 ${getTypography('paragraph-m-12')} text-text-light border border-border-light rounded-lg hover:bg-gray-50 transition-colors`}
            >
              Cancel
            </button>
            <button
              type="button"
              className={`px-6 py-2 ${getTypography('paragraph-m-12')} text-text-dark border border-border-dark rounded-lg hover:bg-gray-50 transition-colors`}
            >
              Save as Draft
            </button>
            <button
              type="submit"
              className={`${createButtonVariant('primary')} ${getTypography('paragraph-m-12')}`}
            >
              Publish Resource
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
