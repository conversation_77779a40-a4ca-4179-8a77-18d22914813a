'use client';

import { useState } from 'react';
import { KeyIcon, ShieldCheckIcon, DevicePhoneMobileIcon } from '@heroicons/react/24/outline';
import { getTypography, createButtonVariant, createCardVariant } from '@/lib/design-tokens';

export function SecuritySettings() {
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Password change requested:', passwordData);
  };

  const handleTwoFactorToggle = () => {
    setTwoFactorEnabled(!twoFactorEnabled);
    console.log('Two-factor authentication:', !twoFactorEnabled ? 'enabled' : 'disabled');
  };

  return (
    <div className="space-y-6">
      {/* Change Password */}
      <div className={`${createCardVariant('bordered')} p-6 space-y-6`}>
        <div className="flex items-center space-x-3">
          <KeyIcon className="w-5 h-5 text-primary" />
          <div>
            <h2 className={`${getTypography('title-b-18')} text-text-primary`}>
              Change Password
            </h2>
            <p className={`${getTypography('paragraph-r-14')} text-text-light`}>
              Update your password to keep your account secure.
            </p>
          </div>
        </div>

        <form onSubmit={handlePasswordSubmit} className="space-y-4">
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Current Password
            </label>
            <input
              type="password"
              value={passwordData.currentPassword}
              onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter your current password"
            />
          </div>
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              New Password
            </label>
            <input
              type="password"
              value={passwordData.newPassword}
              onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter your new password"
            />
          </div>
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Confirm New Password
            </label>
            <input
              type="password"
              value={passwordData.confirmPassword}
              onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Confirm your new password"
            />
          </div>
          <div className="flex justify-end">
            <button
              type="submit"
              className={`${createButtonVariant('primary')} ${getTypography('paragraph-m-12')}`}
            >
              Update Password
            </button>
          </div>
        </form>
      </div>

      {/* Two-Factor Authentication */}
      <div className={`${createCardVariant('bordered')} p-6 space-y-6`}>
        <div className="flex items-center space-x-3">
          <ShieldCheckIcon className="w-5 h-5 text-primary" />
          <div>
            <h2 className={`${getTypography('title-b-18')} text-text-primary`}>
              Two-Factor Authentication
            </h2>
            <p className={`${getTypography('paragraph-r-14')} text-text-light`}>
              Add an extra layer of security to your account.
            </p>
          </div>
        </div>

        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <DevicePhoneMobileIcon className="w-6 h-6 text-text-light" />
            <div>
              <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                Authenticator App
              </p>
              <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                {twoFactorEnabled ? 'Enabled' : 'Use an authenticator app to generate codes'}
              </p>
            </div>
          </div>
          <button
            onClick={handleTwoFactorToggle}
            className={`px-4 py-2 rounded-lg transition-colors ${
              twoFactorEnabled
                ? 'bg-red-100 text-red-700 hover:bg-red-200'
                : 'bg-primary text-white hover:bg-primary-shade'
            } ${getTypography('paragraph-m-12')}`}
          >
            {twoFactorEnabled ? 'Disable' : 'Enable'}
          </button>
        </div>

        {twoFactorEnabled && (
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <p className={`${getTypography('paragraph-m-12')} text-green-800`}>
                Two-factor authentication is enabled
              </p>
              <p className={`${getTypography('paragraph-r-10')} text-green-600 mt-1`}>
                Your account is protected with an additional security layer.
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                className={`${createButtonVariant('secondary')} ${getTypography('paragraph-m-12')}`}
              >
                View Recovery Codes
              </button>
              <button
                className={`${createButtonVariant('secondary')} ${getTypography('paragraph-m-12')}`}
              >
                Regenerate Codes
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Active Sessions */}
      <div className={`${createCardVariant('bordered')} p-6 space-y-6`}>
        <div>
          <h2 className={`${getTypography('title-b-18')} text-text-primary mb-2`}>
            Active Sessions
          </h2>
          <p className={`${getTypography('paragraph-r-14')} text-text-light`}>
            Manage your active sessions across different devices.
          </p>
        </div>

        <div className="space-y-4">
          {[
            {
              device: 'Chrome on Windows',
              location: 'Nairobi, Kenya',
              lastActive: '2 minutes ago',
              current: true,
            },
            {
              device: 'Safari on iPhone',
              location: 'Nairobi, Kenya',
              lastActive: '1 hour ago',
              current: false,
            },
            {
              device: 'Firefox on MacOS',
              location: 'Lagos, Nigeria',
              lastActive: '2 days ago',
              current: false,
            },
          ].map((session, index) => (
            <div key={index} className="flex items-center justify-between p-4 border border-border-light rounded-lg">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  {session.device}
                  {session.current && (
                    <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                      Current
                    </span>
                  )}
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  {session.location} • {session.lastActive}
                </p>
              </div>
              {!session.current && (
                <button
                  className={`px-3 py-1 text-red-600 hover:bg-red-50 rounded ${getTypography('paragraph-m-10')}`}
                >
                  Revoke
                </button>
              )}
            </div>
          ))}
        </div>

        <div className="flex justify-end">
          <button
            className={`px-4 py-2 text-red-600 border border-red-300 rounded-lg hover:bg-red-50 transition-colors ${getTypography('paragraph-m-12')}`}
          >
            Revoke All Other Sessions
          </button>
        </div>
      </div>
    </div>
  );
}
