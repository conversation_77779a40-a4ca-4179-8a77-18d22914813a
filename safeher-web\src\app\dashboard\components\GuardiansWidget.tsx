import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { ChevronRightIcon } from '@heroicons/react/24/outline';

const guardians = [
  {
    id: 1,
    name: '<PERSON>',
    organization: 'SOW Kenya',
    avatar: '👤'
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    organization: 'SOW Kenya',
    avatar: '👤'
  },
  {
    id: 3,
    name: '<PERSON>syoka',
    organization: 'SafeWomenOnline',
    avatar: '👤'
  },
  {
    id: 4,
    name: '<PERSON><PERSON>oka',
    organization: 'Volunteer',
    avatar: '👤'
  },
  {
    id: 5,
    name: '<PERSON>',
    organization: 'Volunteer',
    avatar: '👤'
  }
];

export default function GuardiansWidget() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-semibold">Guardians</CardTitle>
        <button className="text-gray-400 hover:text-gray-600">
          <span className="text-xl">⋯</span>
        </button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {guardians.map((guardian) => (
            <div key={guardian.id} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-lg">{guardian.avatar}</span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">{guardian.name}</p>
                  <p className="text-sm text-gray-600">{guardian.organization}</p>
                </div>
              </div>
              <ChevronRightIcon className="w-5 h-5 text-gray-400" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
