const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class Profile extends Model {
  // Associations
  static associate(models) {
    Profile.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
  }
}

Profile.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    avatar: {
      type: DataTypes.STRING(500),
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    bio: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        len: [10, 20],
      },
    },
    date_of_birth: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    gender: {
      type: DataTypes.ENUM('female', 'male', 'non-binary', 'prefer-not-to-say'),
      allowNull: true,
    },
    location: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    website: {
      type: DataTypes.STRING(500),
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    social_links: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {},
    },
    privacy_settings: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {
        show_email: false,
        show_phone: false,
        show_location: false,
        show_date_of_birth: false
      },
    },
    notification_preferences: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {
        email_notifications: true,
        incident_updates: true,
        resource_updates: false,
        marketing_emails: false
      },
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'Profile',
    tableName: 'profiles',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id'],
      },
      {
        fields: ['gender'],
      },
      {
        fields: ['location'],
      },
    ],
  }
);

module.exports = Profile;
