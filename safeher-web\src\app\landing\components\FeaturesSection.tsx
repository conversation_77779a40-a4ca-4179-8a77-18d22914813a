export default function FeaturesSection() {
  return (
    <section className="bg-gradient-to-r from-blue-500 to-purple-600 py-16 lg:py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            We Made Social Media Safer!
          </h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Feature 1 - Resources */}
          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="mb-6">
              <div className="bg-blue-50 rounded-xl p-4 mb-4">
                <div className="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                  <span className="text-4xl">📚</span>
                </div>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                Quick access to new resources to help you with online violence
              </h3>
            </div>
            
            <div className="bg-blue-900 rounded-xl p-4 text-white">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 bg-blue-700 rounded-full flex items-center justify-center">
                  <span className="text-sm">👤</span>
                </div>
                <div>
                  <p className="font-medium">Hey James</p>
                  <p className="text-xs text-blue-200">Welcome back</p>
                </div>
                <span className="ml-auto text-blue-200">🔔</span>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm font-medium">📊 Insights</p>
                <div className="text-xs text-blue-200 space-y-1">
                  <p>View all • Body Shaming (3) • Cyber Stalking • Cyber Stalking</p>
                </div>
              </div>
              
              <div className="mt-4 bg-white rounded-lg p-3">
                <h4 className="font-bold text-gray-900 mb-2">Body Shaming :</h4>
                <p className="text-xs text-gray-600 leading-relaxed">
                  A guide on how to stop and deal with body Shaming. A guide on how to stop and deal with body Shaming. A guide on how to stop and deal with body Shaming. A guide on how to stop and deal with body Shaming.
                </p>
                <button className="mt-2 text-blue-600 text-xs font-medium">
                  Read Story →
                </button>
              </div>
            </div>
          </div>

          {/* Feature 2 - Reporting */}
          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="mb-6">
              <div className="bg-blue-50 rounded-xl p-4 mb-4">
                <div className="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                  <span className="text-4xl">📝</span>
                </div>
              </div>
            </div>
            
            <div className="bg-white border-2 border-gray-200 rounded-xl p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-bold text-gray-900">Community reporting</h3>
                <button className="text-gray-400">✕</button>
              </div>
              
              <p className="text-sm text-gray-600 mb-4">
                Submit and report your flagged instances of online violence to the SafeHer community.
              </p>
              
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Platform</label>
                  <select className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm">
                    <option>Facebook</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm">
                    <option>Body shaming</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <input 
                    type="text" 
                    placeholder="Body shaming" 
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
                  />
                </div>
                
                <div>
                  <textarea 
                    placeholder="Provide more detail if any" 
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm h-20 resize-none"
                  />
                </div>
                
                <div className="flex space-x-2 pt-2">
                  <button className="px-4 py-2 text-sm text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                    Cancel
                  </button>
                  <button className="px-4 py-2 text-sm text-white bg-blue-600 rounded-lg hover:bg-blue-700">
                    Report
                  </button>
                </div>
              </div>
            </div>
            
            <div className="mt-4 text-center">
              <h3 className="text-xl font-bold text-gray-900">
                Flag and report all incidences of online violence
              </h3>
            </div>
          </div>

          {/* Feature 3 - Community Feedback */}
          <div className="bg-white rounded-2xl p-6 shadow-lg">
            <div className="mb-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Share and get instant feedback on your reports from our community.
              </h3>
            </div>
            
            <div className="bg-gray-50 rounded-xl p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm text-gray-600">← Reports</span>
                <span className="text-sm text-gray-600">🔍</span>
              </div>
              
              <div className="flex space-x-4 text-sm mb-4">
                <span className="text-gray-600">Community</span>
                <span className="text-blue-600 border-b-2 border-blue-600 pb-1">New Reports</span>
                <span className="text-gray-600">My Reports</span>
              </div>
              
              <div className="bg-white rounded-lg p-3 mb-3">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-xs">SH</span>
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm">SafeHer</p>
                    <p className="text-xs text-gray-600 mb-2">
                      This is a case of a sister where the person in the video is seen body shaming women while also...
                    </p>
                    <div className="bg-orange-100 rounded p-2 mb-2">
                      <img src="/api/placeholder/120/80" alt="Report content" className="w-full h-16 object-cover rounded" />
                    </div>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>💬 Comment</span>
                      <span>📤 Send Message</span>
                    </div>
                  </div>
                  <span className="text-gray-400">⋯</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
