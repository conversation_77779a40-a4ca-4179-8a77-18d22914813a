'use client';

import { useState } from 'react';
import { MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline';
import { getTypography, createButtonVariant } from '@/lib/design-tokens';
import { ReportIncidentModal } from './ReportIncidentModal';

const tabs = [
  { id: 'community', label: 'Community' },
  { id: 'new', label: 'New Reports' },
  { id: 'my', label: 'My Reports' },
];

export function ReportsHeader() {
  const [activeTab, setActiveTab] = useState('community');
  const [searchQuery, setSearchQuery] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <div className="flex flex-col space-y-6">
        {/* Page Title and Action */}
        <div className="flex items-center justify-between">
          <h1 className={`${getTypography('title-b-20')} text-text-primary`}>
            Reports
          </h1>
          <button
            onClick={() => setIsModalOpen(true)}
            className={`${createButtonVariant('primary')} ${getTypography('button-r-20')}`}
          >
            Report Incident
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-border-light">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-text-light hover:text-text-dark hover:border-border-dark'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Search and Filter */}
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-text-light" />
            <input
              type="text"
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
          <button className="p-2 border border-border-light rounded-lg hover:bg-gray-50 transition-colors">
            <FunnelIcon className="h-5 w-5 text-text-light" />
          </button>
        </div>
      </div>

      <ReportIncidentModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
      />
    </>
  );
}
