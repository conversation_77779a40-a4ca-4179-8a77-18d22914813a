# SafeHer Backend API

SafeHer is a comprehensive platform designed to combat online violence against women. This repository contains the backend API built with Node.js, Express.js, TypeScript, and MySQL.

## 🚀 Features

- **User Management**: Registration, authentication, and profile management
- **Role-based Access Control**: Admin, Guardian, and User roles with specific permissions
- **Incident Reporting**: Anonymous and authenticated incident reporting system
- **Guardian System**: Expert approval workflow for content moderation
- **Resource Management**: Educational content creation and publishing
- **Report Generation**: Follow-up reporting on incidents
- **JWT Authentication**: Secure token-based authentication
- **API Documentation**: Comprehensive Swagger/OpenAPI documentation
- **Testing Suite**: Complete test coverage with Jest and Supertest

## 🛠️ Tech Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: MySQL with Sequelize ORM
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: Zod
- **Testing**: Jest + Supertest
- **Documentation**: Swagger/OpenAPI
- **Security**: Helmet, CORS, Rate Limiting

## 📋 Prerequisites

- Node.js 18 or higher
- MySQL 8.0 or higher
- npm or yarn package manager

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd safeher-node
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   # Database Configuration
   DB_HOST=localhost
   DB_PORT=3306
   DB_NAME=safeher
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   
   # JWT Configuration
   JWT_SECRET=your_super_secret_jwt_key
   JWT_REFRESH_SECRET=your_super_secret_refresh_key
   
   # Other configurations...
   ```

4. **Set up the database**
   ```bash
   # Create database
   npm run db:create
   
   # Run migrations
   npm run db:migrate
   
   # Seed initial data
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

The API will be available at `http://localhost:3001`

## 📚 API Documentation

Once the server is running, you can access the interactive API documentation at:
- **Swagger UI**: `http://localhost:3001/api/docs`
- **Health Check**: `http://localhost:3001/health`

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Test Environment Setup

1. **Create test database**
   ```bash
   # Copy test environment file
   cp .env.test.example .env.test
   
   # Edit .env.test with test database configuration
   # Make sure DB_NAME contains "test" (e.g., safeher_test)
   ```

2. **Run test database setup**
   ```bash
   NODE_ENV=test npm run db:create
   NODE_ENV=test npm run db:migrate
   ```

## 🗂️ Project Structure

```
src/
├── config/          # Configuration files
│   ├── database.ts  # Database configuration
│   └── sequelize.js # Sequelize CLI configuration
├── controllers/     # Route controllers
│   ├── authController.ts
│   ├── userController.ts
│   ├── guardianController.ts
│   ├── incidentController.ts
│   └── resourceController.ts
├── middleware/      # Custom middleware
│   ├── auth.ts      # Authentication middleware
│   ├── validation.ts # Validation middleware
│   └── errorHandler.ts
├── models/          # Sequelize models
│   ├── index.ts
│   ├── Role.ts
│   ├── User.ts
│   ├── Guardian.ts
│   ├── Incident.ts
│   ├── Report.ts
│   └── Resource.ts
├── routes/          # API routes
│   ├── auth.ts
│   ├── users.ts
│   ├── guardians.ts
│   ├── incidents.ts
│   └── resources.ts
├── utils/           # Utility functions
│   └── jwt.ts
├── validators/      # Zod validation schemas
│   └── auth.ts
├── migrations/      # Database migrations
├── seeders/         # Database seeders
├── tests/           # Test files
│   ├── helpers/
│   ├── auth.test.ts
│   └── incidents.test.ts
└── index.ts         # Application entry point
```

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Default Admin Account

After running the seeders, a default admin account is created:
- **Email**: `<EMAIL>`
- **Password**: `SafeHer@Admin123!`

⚠️ **Important**: Change the default password after first login!

## 🛡️ Security Features

- **Password Hashing**: bcrypt with salt rounds
- **JWT Tokens**: Access and refresh token system
- **Rate Limiting**: API rate limiting to prevent abuse
- **CORS**: Cross-origin resource sharing configuration
- **Helmet**: Security headers middleware
- **Input Validation**: Comprehensive request validation with Zod
- **SQL Injection Protection**: Sequelize ORM with parameterized queries

## 📊 Database Schema

### Core Entities

- **Users**: User accounts with role-based access
- **Roles**: Admin, Guardian, User roles with permissions
- **Guardians**: Expert profiles for content moderation
- **Incidents**: Reported incidents with status tracking
- **Reports**: Follow-up reports on incidents
- **Resources**: Educational content and resources

### Key Relationships

- Users belong to Roles
- Guardians have one-to-one relationship with Users
- Incidents can be assigned to Guardians
- Resources are authored by Guardians/Admins
- Reports are linked to Incidents

## 🚀 Deployment

### Environment Variables

Ensure all required environment variables are set:

```env
NODE_ENV=production
DB_HOST=your_production_db_host
DB_NAME=your_production_db_name
DB_USER=your_production_db_user
DB_PASSWORD=your_production_db_password
JWT_SECRET=your_production_jwt_secret
JWT_REFRESH_SECRET=your_production_refresh_secret
```

### Production Build

```bash
# Build the application
npm run build

# Start production server
npm start
```

## 📝 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/register-guardian` - Guardian application
- `GET /api/auth/profile` - Get user profile
- `POST /api/auth/logout` - User logout
- `POST /api/auth/refresh` - Refresh JWT token

### Users
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `PUT /api/users/password` - Change password
- `DELETE /api/users/account` - Deactivate account

### Guardians
- `GET /api/guardians` - List guardians (Admin only)
- `PUT /api/guardians/:id/approve` - Approve guardian (Admin only)
- `PUT /api/guardians/:id/reject` - Reject guardian (Admin only)
- `PUT /api/guardians/:id/revoke` - Revoke guardian access (Admin only)

### Incidents
- `POST /api/incidents` - Create incident report
- `GET /api/incidents` - List incidents
- `GET /api/incidents/:id` - Get incident details
- `PUT /api/incidents/:id/status` - Update incident status
- `PUT /api/incidents/:id/assign` - Assign guardian to incident

### Resources
- `POST /api/resources` - Create resource (Guardian/Admin only)
- `GET /api/resources` - List resources
- `GET /api/resources/:id` - Get resource details
- `PUT /api/resources/:id` - Update resource
- `DELETE /api/resources/:id` - Delete resource
- `POST /api/resources/:id/like` - Like resource

For detailed API documentation with request/response schemas, visit the Swagger UI at `/api/docs` when the server is running.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions, please contact the development team or create an issue in the repository.

---

**SafeHer** - Empowering women's safety in the digital world 🛡️
