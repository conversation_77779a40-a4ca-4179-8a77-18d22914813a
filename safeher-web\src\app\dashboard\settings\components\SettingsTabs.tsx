'use client';

import { useState } from 'react';
import { 
  UserIcon, 
  ShieldCheckIcon, 
  BellIcon, 
  GlobeAltIcon,
  KeyIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import { getTypography } from '@/lib/design-tokens';
import { ProfileSettings } from './ProfileSettings';
import { PrivacySettings } from './PrivacySettings';
import { NotificationSettings } from './NotificationSettings';
import { SecuritySettings } from './SecuritySettings';
import { PreferencesSettings } from './PreferencesSettings';
import { AccountSettings } from './AccountSettings';

const tabs = [
  { id: 'profile', label: 'Profile', icon: UserIcon },
  { id: 'privacy', label: 'Privacy', icon: ShieldCheckIcon },
  { id: 'notifications', label: 'Notifications', icon: BellIcon },
  { id: 'security', label: 'Security', icon: KeyIcon },
  { id: 'preferences', label: 'Preferences', icon: GlobeAltIcon },
  { id: 'account', label: 'Account', icon: CogIcon },
];

export function SettingsTabs() {
  const [activeTab, setActiveTab] = useState('profile');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return <ProfileSettings />;
      case 'privacy':
        return <PrivacySettings />;
      case 'notifications':
        return <NotificationSettings />;
      case 'security':
        return <SecuritySettings />;
      case 'preferences':
        return <PreferencesSettings />;
      case 'account':
        return <AccountSettings />;
      default:
        return <ProfileSettings />;
    }
  };

  return (
    <div className="flex flex-col lg:flex-row gap-6">
      {/* Sidebar Navigation */}
      <div className="lg:w-64 flex-shrink-0">
        <nav className="space-y-1">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-primary text-white'
                    : 'text-text-dark hover:bg-gray-50'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className={`${getTypography('paragraph-m-14')}`}>
                  {tab.label}
                </span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content Area */}
      <div className="flex-1">
        {renderTabContent()}
      </div>
    </div>
  );
}
