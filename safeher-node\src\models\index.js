const { sequelize } = require('../config/database');
const Role = require('./Role');
const User = require('./User');
const Profile = require('./Profile');
const Guardian = require('./Guardian');
const Incident = require('./Incident');
const Report = require('./Report');
const Resource = require('./Resource');

// Define all models
const models = {
  Role,
  User,
  Profile,
  Guardian,
  Incident,
  Report,
  Resource,
};

// Set up associations
Object.values(models).forEach((model) => {
  if (model.associate) {
    model.associate(models);
  }
});

// Export models and sequelize instance
module.exports = {
  sequelize,
  Role,
  User,
  Profile,
  Guardian,
  Incident,
  Report,
  Resource,
};
