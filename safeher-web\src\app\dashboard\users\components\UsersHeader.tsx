'use client';

import { useState } from 'react';
import { MagnifyingGlassIcon, FunnelIcon, UserPlusIcon } from '@heroicons/react/24/outline';
import { getTypography, createButtonVariant } from '@/lib/design-tokens';

export function UsersHeader() {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="flex flex-col space-y-6">
      {/* Page Title and Action */}
      <div className="flex items-center justify-between">
        <h1 className={`${getTypography('title-b-20')} text-text-primary`}>
          Users
        </h1>
        <button className={`${createButtonVariant('primary')} ${getTypography('button-r-20')} flex items-center space-x-2`}>
          <UserPlusIcon className="w-5 h-5" />
          <span>Add User</span>
        </button>
      </div>

      {/* Search and Filter */}
      <div className="flex items-center space-x-4">
        <div className="flex-1 relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-text-light" />
          <input
            type="text"
            placeholder="Search users..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        </div>
        <button className="p-2 border border-border-light rounded-lg hover:bg-gray-50 transition-colors">
          <FunnelIcon className="h-5 w-5 text-text-light" />
        </button>
      </div>
    </div>
  );
}
