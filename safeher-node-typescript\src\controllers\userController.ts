import { Response } from 'express';
import { User, Role, Guardian } from '../models';
import { CustomError, asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest, ApiResponse } from '../types';

export const getMe = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const user = await User.findByPk(req.user!.id, {
    include: [
      {
        model: Role,
        as: 'role',
        attributes: ['id', 'name'],
      },
      {
        model: Guardian,
        as: 'guardian',
        attributes: ['id', 'designation', 'organization', 'status', 'resources_count', 'reports_count'],
      },
    ],
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  const response: ApiResponse = {
    success: true,
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role?.name || 'user',
        avatar: user.avatar,
        isActive: user.is_active,
        emailVerified: user.email_verified,
        lastLogin: user.last_login,
        createdAt: user.created_at,
        guardian: user.guardian,
      },
    },
  };

  res.status(200).json(response);
});

export const updateProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { name, avatar } = req.body;
  const userId = req.user!.id;

  const user = await User.findByPk(userId);
  if (!user) {
    throw new CustomError('User not found', 404);
  }

  // Update user profile
  await user.update({
    name: name || user.name,
    avatar: avatar || user.avatar,
  });

  // Fetch updated user with relations
  const updatedUser = await User.findByPk(userId, {
    include: [
      {
        model: Role,
        as: 'role',
        attributes: ['id', 'name'],
      },
      {
        model: Guardian,
        as: 'guardian',
        attributes: ['id', 'designation', 'organization', 'status'],
      },
    ],
  });

  const response: ApiResponse = {
    success: true,
    message: 'Profile updated successfully',
    data: {
      user: {
        id: updatedUser!.id,
        name: updatedUser!.name,
        email: updatedUser!.email,
        role: updatedUser!.role?.name || 'user',
        avatar: updatedUser!.avatar,
        isActive: updatedUser!.is_active,
        emailVerified: updatedUser!.email_verified,
        lastLogin: updatedUser!.last_login,
        createdAt: updatedUser!.created_at,
        guardian: updatedUser!.guardian,
      },
    },
  };

  res.status(200).json(response);
});

export const changePassword = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user!.id;

  const user = await User.findByPk(userId);
  if (!user) {
    throw new CustomError('User not found', 404);
  }

  // Verify current password
  const isValidPassword = await user.validatePassword(currentPassword);
  if (!isValidPassword) {
    throw new CustomError('Current password is incorrect', 400);
  }

  // Update password
  await user.update({ password_hash: newPassword });

  const response: ApiResponse = {
    success: true,
    message: 'Password changed successfully',
  };

  res.status(200).json(response);
});

export const deactivateAccount = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  const user = await User.findByPk(userId);
  if (!user) {
    throw new CustomError('User not found', 404);
  }

  await user.update({ is_active: false });

  const response: ApiResponse = {
    success: true,
    message: 'Account deactivated successfully',
  };

  res.status(200).json(response);
});
