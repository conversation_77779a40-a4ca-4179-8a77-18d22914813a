const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class Guardian extends Model {
  // Associations
  static associate(models) {
    Guardian.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });

    Guardian.belongsTo(models.User, {
      foreignKey: 'approved_by',
      as: 'approver'
    });

    Guardian.hasMany(models.Incident, {
      foreignKey: 'guardian_id',
      as: 'assignedIncidents'
    });

    Guardian.hasMany(models.Report, {
      foreignKey: 'guardian_id',
      as: 'reports'
    });
  }
}

Guardian.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    designation: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        len: [2, 255],
        notEmpty: true,
      },
    },
    organization: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    bio: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    expertise_areas: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected', 'suspended'),
      allowNull: false,
      defaultValue: 'pending',
      validate: {
        isIn: [['pending', 'approved', 'rejected', 'suspended']],
      },
    },
    approved_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    approved_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    resources_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0,
      },
    },
    reports_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0,
      },
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'Guardian',
    tableName: 'guardians',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['approved_by'],
      },
    ],
    hooks: {
      afterUpdate: async (guardian) => {
        // Update user role when guardian is approved
        if (guardian.changed('status') && guardian.status === 'approved') {
          const { User, Role } = sequelize.models;
          const guardianRole = await Role.findOne({ where: { name: 'guardian' } });
          if (guardianRole) {
            await User.update(
              { role_id: guardianRole.id },
              { where: { id: guardian.user_id } }
            );
          }
        }
      },
    },
  }
);

module.exports = Guardian;
