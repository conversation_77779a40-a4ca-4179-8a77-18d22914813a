'use client';

import { useState } from 'react';
import { getTypography, createButtonVariant, createCardVariant } from '@/lib/design-tokens';

export function PrivacySettings() {
  const [settings, setSettings] = useState({
    profileVisibility: 'public',
    showEmail: false,
    showPhone: false,
    allowMessages: true,
    showActivity: true,
    dataCollection: true,
    analyticsOptIn: false,
    marketingEmails: false,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Privacy settings updated:', settings);
  };

  return (
    <div className={`${createCardVariant('bordered')} p-6 space-y-6`}>
      <div>
        <h2 className={`${getTypography('title-b-18')} text-text-primary mb-2`}>
          Privacy Settings
        </h2>
        <p className={`${getTypography('paragraph-r-14')} text-text-light`}>
          Control who can see your information and how your data is used.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Profile Visibility */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Profile Visibility
          </h3>
          <div className="space-y-3">
            {[
              { value: 'public', label: 'Public', description: 'Anyone can see your profile' },
              { value: 'community', label: 'SafeHer Community', description: 'Only SafeHer members can see your profile' },
              { value: 'private', label: 'Private', description: 'Only you can see your profile' },
            ].map((option) => (
              <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="profileVisibility"
                  value={option.value}
                  checked={settings.profileVisibility === option.value}
                  onChange={(e) => setSettings({ ...settings, profileVisibility: e.target.value })}
                  className="mt-1 h-4 w-4 text-primary focus:ring-primary border-border-dark"
                />
                <div>
                  <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                    {option.label}
                  </p>
                  <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                    {option.description}
                  </p>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Contact Information */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Contact Information
          </h3>
          <div className="space-y-3">
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Show email address
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Allow others to see your email address
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.showEmail}
                onChange={(e) => setSettings({ ...settings, showEmail: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Show phone number
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Allow others to see your phone number
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.showPhone}
                onChange={(e) => setSettings({ ...settings, showPhone: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
          </div>
        </div>

        {/* Communication */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Communication
          </h3>
          <div className="space-y-3">
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Allow direct messages
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Let other users send you direct messages
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.allowMessages}
                onChange={(e) => setSettings({ ...settings, allowMessages: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Show activity status
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Let others see when you're online
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.showActivity}
                onChange={(e) => setSettings({ ...settings, showActivity: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
          </div>
        </div>

        {/* Data & Analytics */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Data & Analytics
          </h3>
          <div className="space-y-3">
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Data collection for service improvement
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Help us improve SafeHer by sharing usage data
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.dataCollection}
                onChange={(e) => setSettings({ ...settings, dataCollection: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Analytics and performance tracking
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Allow analytics cookies for better user experience
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.analyticsOptIn}
                onChange={(e) => setSettings({ ...settings, analyticsOptIn: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Marketing communications
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Receive emails about SafeHer updates and features
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.marketingEmails}
                onChange={(e) => setSettings({ ...settings, marketingEmails: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-border-light">
          <button
            type="button"
            className={`px-6 py-2 ${getTypography('paragraph-m-12')} text-text-light border border-border-light rounded-lg hover:bg-gray-50 transition-colors`}
          >
            Reset to Default
          </button>
          <button
            type="submit"
            className={`${createButtonVariant('primary')} ${getTypography('paragraph-m-12')}`}
          >
            Save Privacy Settings
          </button>
        </div>
      </form>
    </div>
  );
}
