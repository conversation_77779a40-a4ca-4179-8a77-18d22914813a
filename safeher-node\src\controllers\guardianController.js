const { Guardian, User, Role } = require('../models');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

const getGuardians = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    status,
    search
  } = req.query;

  // Only admins can see all guardians, others see only approved ones
  const whereClause = {};
  
  if (req.user?.role !== 'admin') {
    whereClause.status = 'approved';
  } else if (status) {
    whereClause.status = status;
  }

  const offset = (parseInt(page) - 1) * parseInt(limit);

  const { rows: guardians, count } = await Guardian.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'avatar', 'created_at'],
        where: search ? {
          [Op.or]: [
            { name: { [Op.like]: `%${search}%` } },
            { email: { [Op.like]: `%${search}%` } }
          ]
        } : undefined
      },
      {
        model: User,
        as: 'approver',
        attributes: ['id', 'name'],
        required: false
      }
    ],
    limit: parseInt(limit),
    offset,
    order: [['created_at', 'DESC']]
  });

  const response = {
    success: true,
    data: {
      guardians: guardians.map(guardian => ({
        id: guardian.id,
        designation: guardian.designation,
        organization: guardian.organization,
        bio: guardian.bio,
        expertiseAreas: guardian.expertise_areas,
        status: guardian.status,
        resourcesCount: guardian.resources_count,
        reportsCount: guardian.reports_count,
        approvedAt: guardian.approved_at,
        user: {
          id: guardian.user.id,
          name: guardian.user.name,
          email: req.user?.role === 'admin' ? guardian.user.email : undefined,
          avatar: guardian.user.avatar,
          joinedAt: guardian.user.created_at
        },
        approver: guardian.approver ? {
          id: guardian.approver.id,
          name: guardian.approver.name
        } : null,
        createdAt: guardian.created_at,
        updatedAt: guardian.updated_at
      })),
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / parseInt(limit))
      }
    }
  };

  res.status(200).json(response);
});

const getGuardianById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const guardian = await Guardian.findByPk(id, {
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'avatar', 'created_at']
      },
      {
        model: User,
        as: 'approver',
        attributes: ['id', 'name'],
        required: false
      }
    ]
  });

  if (!guardian) {
    throw new CustomError('Guardian not found', 404);
  }

  // Only show approved guardians to non-admins
  if (req.user?.role !== 'admin' && guardian.status !== 'approved') {
    throw new CustomError('Guardian not found', 404);
  }

  const response = {
    success: true,
    data: {
      guardian: {
        id: guardian.id,
        designation: guardian.designation,
        organization: guardian.organization,
        bio: guardian.bio,
        expertiseAreas: guardian.expertise_areas,
        status: guardian.status,
        resourcesCount: guardian.resources_count,
        reportsCount: guardian.reports_count,
        approvedAt: guardian.approved_at,
        user: {
          id: guardian.user.id,
          name: guardian.user.name,
          email: req.user?.role === 'admin' ? guardian.user.email : undefined,
          avatar: guardian.user.avatar,
          joinedAt: guardian.user.created_at
        },
        approver: guardian.approver ? {
          id: guardian.approver.id,
          name: guardian.approver.name
        } : null,
        createdAt: guardian.created_at,
        updatedAt: guardian.updated_at
      }
    }
  };

  res.status(200).json(response);
});

const approveGuardian = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Only admins can approve guardians
  if (req.user?.role !== 'admin') {
    throw new CustomError('Only admins can approve guardians', 403);
  }

  const guardian = await Guardian.findByPk(id, {
    include: [
      {
        model: User,
        as: 'user'
      }
    ]
  });

  if (!guardian) {
    throw new CustomError('Guardian not found', 404);
  }

  if (guardian.status !== 'pending') {
    throw new CustomError('Guardian is not in pending status', 400);
  }

  // Update guardian status
  await guardian.update({
    status: 'approved',
    approved_by: req.user.id,
    approved_at: new Date()
  });

  // Update user role to guardian
  const guardianRole = await Role.findOne({ where: { name: 'guardian' } });
  if (guardianRole) {
    await guardian.user.update({ role_id: guardianRole.id });
  }

  const response = {
    success: true,
    message: 'Guardian approved successfully',
    data: {
      guardian: {
        id: guardian.id,
        status: guardian.status,
        approvedAt: guardian.approved_at,
        approvedBy: req.user.id
      }
    }
  };

  res.status(200).json(response);
});

const rejectGuardian = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { reason } = req.body;

  // Only admins can reject guardians
  if (req.user?.role !== 'admin') {
    throw new CustomError('Only admins can reject guardians', 403);
  }

  const guardian = await Guardian.findByPk(id);

  if (!guardian) {
    throw new CustomError('Guardian not found', 404);
  }

  if (guardian.status !== 'pending') {
    throw new CustomError('Guardian is not in pending status', 400);
  }

  // Update guardian status
  await guardian.update({
    status: 'rejected',
    approved_by: req.user.id,
    approved_at: new Date()
  });

  const response = {
    success: true,
    message: 'Guardian application rejected',
    data: {
      guardian: {
        id: guardian.id,
        status: guardian.status,
        rejectedAt: guardian.approved_at,
        rejectedBy: req.user.id,
        reason
      }
    }
  };

  res.status(200).json(response);
});

const suspendGuardian = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { reason } = req.body;

  // Only admins can suspend guardians
  if (req.user?.role !== 'admin') {
    throw new CustomError('Only admins can suspend guardians', 403);
  }

  const guardian = await Guardian.findByPk(id, {
    include: [
      {
        model: User,
        as: 'user'
      }
    ]
  });

  if (!guardian) {
    throw new CustomError('Guardian not found', 404);
  }

  if (guardian.status !== 'approved') {
    throw new CustomError('Guardian is not approved', 400);
  }

  // Update guardian status
  await guardian.update({
    status: 'suspended'
  });

  // Revert user role back to user
  const userRole = await Role.findOne({ where: { name: 'user' } });
  if (userRole) {
    await guardian.user.update({ role_id: userRole.id });
  }

  const response = {
    success: true,
    message: 'Guardian suspended successfully',
    data: {
      guardian: {
        id: guardian.id,
        status: guardian.status,
        suspendedBy: req.user.id,
        reason
      }
    }
  };

  res.status(200).json(response);
});

const updateGuardianProfile = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { designation, organization, bio, expertiseAreas } = req.body;

  const guardian = await Guardian.findByPk(id);

  if (!guardian) {
    throw new CustomError('Guardian not found', 404);
  }

  // Check permissions - guardians can only update their own profile
  if (req.user?.role !== 'admin' && guardian.user_id !== req.user?.id) {
    throw new CustomError('You can only update your own guardian profile', 403);
  }

  const updateData = {};
  if (designation !== undefined) updateData.designation = designation;
  if (organization !== undefined) updateData.organization = organization;
  if (bio !== undefined) updateData.bio = bio;
  if (expertiseAreas !== undefined) updateData.expertise_areas = expertiseAreas;

  await guardian.update(updateData);

  const response = {
    success: true,
    message: 'Guardian profile updated successfully',
    data: {
      guardian: {
        id: guardian.id,
        designation: guardian.designation,
        organization: guardian.organization,
        bio: guardian.bio,
        expertiseAreas: guardian.expertise_areas,
        updatedAt: guardian.updated_at
      }
    }
  };

  res.status(200).json(response);
});

module.exports = {
  getGuardians,
  getGuardianById,
  approveGuardian,
  rejectGuardian,
  suspendGuardian,
  updateGuardianProfile
};
