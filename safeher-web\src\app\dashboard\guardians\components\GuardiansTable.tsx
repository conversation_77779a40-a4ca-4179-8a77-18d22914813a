'use client';

import { useState } from 'react';
import { EllipsisVerticalIcon } from '@heroicons/react/24/outline';

const guardians = [
  {
    id: 1,
    name: '<PERSON>',
    email: 'j<PERSON><PERSON><PERSON>@gmail.com',
    joinedDate: 'Since 19/10/2022',
    resources: 34,
    reports: 400,
    designation: 'Volunteer',
    avatar: 'https://i.pravatar.cc/40?img=5'
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    joinedDate: 'Since 19/10/2022',
    resources: 40,
    reports: 250,
    designation: 'Sow Kenya',
    avatar: 'https://i.pravatar.cc/40?img=12'
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
    joinedDate: 'Since 19/10/2022',
    resources: 40,
    reports: 400,
    designation: 'Sow Kenya',
    avatar: 'https://i.pravatar.cc/40?img=15'
  },
  {
    id: 4,
    name: '<PERSON>',
    email: 'olive.mus<PERSON><PERSON>@safewomen.co',
    joinedDate: 'Since 19/10/2022',
    resources: 40,
    reports: 450,
    designation: 'Safewomen',
    avatar: 'https://i.pravatar.cc/40?img=20'
  },
  {
    id: 5,
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    joinedDate: 'Since 19/10/2022',
    resources: 40,
    reports: 600,
    designation: 'Volunteer',
    avatar: 'https://i.pravatar.cc/40?img=8'
  },
  {
    id: 6,
    name: 'Oscar Tiego',
    email: '<EMAIL>',
    joinedDate: 'Since 19/10/2022',
    resources: 40,
    reports: 60,
    designation: 'Volunteer',
    avatar: 'https://i.pravatar.cc/40?img=32'
  },
  {
    id: 7,
    name: 'Andy Young',
    email: '<EMAIL>',
    joinedDate: 'Since 19/10/2022',
    resources: 40,
    reports: 650,
    designation: 'Volunteer',
    avatar: 'https://i.pravatar.cc/40?img=45'
  }
];

export default function GuardiansTable() {
  const [currentPage, setCurrentPage] = useState(2);
  const totalPages = 10;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-4 px-6 font-semibold text-gray-700">Guardian</th>
              <th className="text-left py-4 px-6 font-semibold text-gray-700">Email</th>
              <th className="text-center py-4 px-6 font-semibold text-gray-700">Resources</th>
              <th className="text-center py-4 px-6 font-semibold text-gray-700">Reports</th>
              <th className="text-center py-4 px-6 font-semibold text-gray-700">Designation</th>
              <th className="text-center py-4 px-6 font-semibold text-gray-700"></th>
            </tr>
          </thead>
          <tbody>
            {guardians.map((guardian, index) => (
              <tr key={guardian.id} className={`${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'} hover:bg-gray-100 transition-colors`}>
                <td className="py-4 px-6">
                  <div className="flex items-center space-x-3">
                    <img
                      src={guardian.avatar}
                      alt={guardian.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <div>
                      <p className="font-semibold text-gray-900">{guardian.name}</p>
                      <p className="text-sm text-gray-500">{guardian.joinedDate}</p>
                    </div>
                  </div>
                </td>
                <td className="py-4 px-6 text-gray-700">{guardian.email}</td>
                <td className="py-4 px-6 text-center text-gray-700">{guardian.resources}</td>
                <td className="py-4 px-6 text-center text-gray-700">{guardian.reports}</td>
                <td className="py-4 px-6 text-center text-gray-700">{guardian.designation}</td>
                <td className="py-4 px-6 text-center">
                  <button className="text-gray-400 hover:text-gray-600">
                    <EllipsisVerticalIcon className="w-5 h-5" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200">
        <button 
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
          disabled={currentPage === 1}
        >
          Previous
        </button>
        
        <div className="flex items-center space-x-2">
          {[1, 2, 3, '...', 8, 9, 10].map((page, index) => (
            <button
              key={index}
              className={`px-3 py-2 text-sm font-medium rounded-lg ${
                page === currentPage
                  ? 'bg-white text-gray-900 border border-gray-300'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => typeof page === 'number' && setCurrentPage(page)}
              disabled={page === '...'}
            >
              {page}
            </button>
          ))}
        </div>
        
        <button 
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
          disabled={currentPage === totalPages}
        >
          Next
        </button>
      </div>
    </div>
  );
}
