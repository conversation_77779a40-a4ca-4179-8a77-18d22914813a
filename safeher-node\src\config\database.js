const { Sequelize } = require('sequelize');
require('dotenv').config();

const isTest = process.env.NODE_ENV === 'test';

const dbConfig = {
  host: isTest ? process.env.TEST_DB_HOST : process.env.DB_HOST || 'localhost',
  port: parseInt(isTest ? process.env.TEST_DB_PORT || '3306' : process.env.DB_PORT || '3306'),
  database: isTest ? process.env.TEST_DB_NAME : process.env.DB_NAME || 'safeher_db',
  username: isTest ? process.env.TEST_DB_USER : process.env.DB_USER || 'root',
  password: isTest ? process.env.TEST_DB_PASSWORD : process.env.DB_PASSWORD || '',
  dialect: 'mysql',
};

const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  }
);

// Test database connection
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ Database connection has been established successfully.');
    return true;
  } catch (error) {
    console.error('❌ Unable to connect to the database:', error);
    return false;
  }
};

module.exports = { sequelize, testConnection };
