import WebsiteLayout from '@/components/global/layout/WebsiteLayout';
import Button from '@/components/ui/Button';
import Link from 'next/link';

export default function ForGuardiansPage() {
  return (
    <WebsiteLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-purple-600 to-blue-600 py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-white">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              Become a SafeHer Guardian
            </h1>
            <p className="text-xl leading-relaxed max-w-3xl mx-auto mb-8">
              Join our community of dedicated volunteers and professionals working to make 
              the internet a safer place for women. Your expertise and compassion can make 
              a real difference.
            </p>
            <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
              Apply to be a Guardian
            </Button>
          </div>
        </div>
      </section>

      {/* What is a Guardian Section */}
      <section className="bg-white py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              What is a SafeHer Guardian?
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
              Guardians are trained volunteers and professionals who provide support, 
              guidance, and resources to women experiencing online violence and harassment.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">🛡️</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Support Provider</h3>
              <p className="text-gray-600">
                Offer emotional support and practical guidance to women facing online harassment
              </p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">📚</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Resource Creator</h3>
              <p className="text-gray-600">
                Develop and share educational content to help women stay safe online
              </p>
            </div>
            <div className="text-center">
              <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">🤝</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Community Builder</h3>
              <p className="text-gray-600">
                Help foster a supportive community where women feel safe to share and seek help
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Requirements Section */}
      <section className="bg-gray-50 py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Guardian Requirements
              </h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="bg-green-500 rounded-full w-6 h-6 flex items-center justify-center mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <p className="text-gray-700">
                    Commitment to women's safety and digital rights
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="bg-green-500 rounded-full w-6 h-6 flex items-center justify-center mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <p className="text-gray-700">
                    Ability to provide emotional support with empathy and understanding
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="bg-green-500 rounded-full w-6 h-6 flex items-center justify-center mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <p className="text-gray-700">
                    Basic understanding of online safety and digital literacy
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="bg-green-500 rounded-full w-6 h-6 flex items-center justify-center mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <p className="text-gray-700">
                    Willingness to complete our training program
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="bg-green-500 rounded-full w-6 h-6 flex items-center justify-center mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <p className="text-gray-700">
                    Minimum 5 hours per week availability
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                What We Provide
              </h2>
              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <h3 className="font-semibold text-gray-900 mb-2">Comprehensive Training</h3>
                  <p className="text-gray-600 text-sm">
                    Complete training on online violence, support techniques, and platform tools
                  </p>
                </div>
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <h3 className="font-semibold text-gray-900 mb-2">Ongoing Support</h3>
                  <p className="text-gray-600 text-sm">
                    Regular check-ins, peer support groups, and professional development
                  </p>
                </div>
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <h3 className="font-semibold text-gray-900 mb-2">Recognition</h3>
                  <p className="text-gray-600 text-sm">
                    Certificates, references, and public recognition for your contributions
                  </p>
                </div>
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <h3 className="font-semibold text-gray-900 mb-2">Community</h3>
                  <p className="text-gray-600 text-sm">
                    Join a network of like-minded individuals working for positive change
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-white py-16 lg:py-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
            Ready to Make a Difference?
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed mb-8">
            Join our guardian community and help create a safer digital world for women everywhere.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg">
              Apply Now
            </Button>
            <Button variant="outline" size="lg">
              Learn More
            </Button>
          </div>
        </div>
      </section>
    </WebsiteLayout>
  );
}
