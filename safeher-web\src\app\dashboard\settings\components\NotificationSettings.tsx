'use client';

import { useState } from 'react';
import { getTypography, createButtonVariant, createCardVariant } from '@/lib/design-tokens';

export function NotificationSettings() {
  const [settings, setSettings] = useState({
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    newReports: true,
    reportUpdates: true,
    communityActivity: false,
    resourceUpdates: true,
    securityAlerts: true,
    weeklyDigest: true,
    monthlyReport: false,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Notification settings updated:', settings);
  };

  return (
    <div className={`${createCardVariant('bordered')} p-6 space-y-6`}>
      <div>
        <h2 className={`${getTypography('title-b-18')} text-text-primary mb-2`}>
          Notification Preferences
        </h2>
        <p className={`${getTypography('paragraph-r-14')} text-text-light`}>
          Choose how you want to be notified about SafeHer activities.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Notification Methods */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Notification Methods
          </h3>
          <div className="space-y-3">
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Email notifications
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Receive notifications via email
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.emailNotifications}
                onChange={(e) => setSettings({ ...settings, emailNotifications: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Push notifications
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Receive browser and mobile push notifications
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.pushNotifications}
                onChange={(e) => setSettings({ ...settings, pushNotifications: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  SMS notifications
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Receive important alerts via SMS
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.smsNotifications}
                onChange={(e) => setSettings({ ...settings, smsNotifications: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
          </div>
        </div>

        {/* Activity Notifications */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Activity Notifications
          </h3>
          <div className="space-y-3">
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  New reports in community
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Get notified when new reports are submitted
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.newReports}
                onChange={(e) => setSettings({ ...settings, newReports: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Report status updates
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Updates on reports you've submitted or are following
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.reportUpdates}
                onChange={(e) => setSettings({ ...settings, reportUpdates: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Community activity
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Comments, likes, and other community interactions
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.communityActivity}
                onChange={(e) => setSettings({ ...settings, communityActivity: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  New resources
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Get notified about new safety resources and articles
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.resourceUpdates}
                onChange={(e) => setSettings({ ...settings, resourceUpdates: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
          </div>
        </div>

        {/* Security & System */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Security & System
          </h3>
          <div className="space-y-3">
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Security alerts
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Important security notifications and login alerts
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.securityAlerts}
                onChange={(e) => setSettings({ ...settings, securityAlerts: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
          </div>
        </div>

        {/* Digest & Reports */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Digest & Reports
          </h3>
          <div className="space-y-3">
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Weekly digest
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Weekly summary of community activity and resources
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.weeklyDigest}
                onChange={(e) => setSettings({ ...settings, weeklyDigest: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Monthly safety report
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Monthly insights and safety trends
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.monthlyReport}
                onChange={(e) => setSettings({ ...settings, monthlyReport: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-border-light">
          <button
            type="button"
            className={`px-6 py-2 ${getTypography('paragraph-m-12')} text-text-light border border-border-light rounded-lg hover:bg-gray-50 transition-colors`}
          >
            Disable All
          </button>
          <button
            type="submit"
            className={`${createButtonVariant('primary')} ${getTypography('paragraph-m-12')}`}
          >
            Save Notification Settings
          </button>
        </div>
      </form>
    </div>
  );
}
