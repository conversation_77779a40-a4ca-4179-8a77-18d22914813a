'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('profiles', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.INTEGER,
        autoIncrement: true
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        unique: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      avatar: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      bio: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      phone: {
        type: Sequelize.STRING(20),
        allowNull: true
      },
      date_of_birth: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      gender: {
        type: Sequelize.ENUM('female', 'male', 'non-binary', 'prefer-not-to-say'),
        allowNull: true
      },
      location: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      website: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      social_links: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: '{}'
      },
      privacy_settings: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: '{"show_email": false, "show_phone": false, "show_location": false, "show_date_of_birth": false}'
      },
      notification_preferences: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: '{"email_notifications": true, "incident_updates": true, "resource_updates": false, "marketing_emails": false}'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('profiles', ['user_id']);
    await queryInterface.addIndex('profiles', ['gender']);
    await queryInterface.addIndex('profiles', ['location']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('profiles');
  }
};
