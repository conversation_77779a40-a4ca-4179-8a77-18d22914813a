# Test Environment Configuration
NODE_ENV=test

# Database Configuration (Test Database)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=safeher_test
DB_USER=your_test_db_user
DB_PASSWORD=your_test_db_password

# JWT Configuration
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_REFRESH_SECRET=test_jwt_refresh_secret_key_for_testing_only
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# API Configuration
PORT=3002
API_URL=http://localhost:3002
FRONTEND_URL=http://localhost:3000

# File Upload Configuration (Test)
UPLOAD_DIR=./uploads/test
MAX_FILE_SIZE=5242880

# Email Configuration (Test - use test service)
EMAIL_SERVICE=test
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=test_password
EMAIL_FROM=SafeHer Test <<EMAIL>>

# Rate Limiting (More lenient for testing)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Logging
LOG_LEVEL=error
