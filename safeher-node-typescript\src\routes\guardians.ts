import { Router } from 'express';
import {
  get<PERSON><PERSON><PERSON>,
  approve<PERSON><PERSON><PERSON>,
  reject<PERSON><PERSON><PERSON>,
  revoke<PERSON><PERSON><PERSON>,
  getGuardianById,
} from '../controllers/guardianController';
import { authenticate, requireAdmin, requireGuardianOrAdmin } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { z } from 'zod';

const router = Router();

// Validation schemas
const guardianActionSchema = z.object({
  body: z.object({
    reason: z.string().max(500).optional(),
  }),
});

/**
 * @swagger
 * /api/guardians:
 *   get:
 *     summary: Get paginated list of guardians
 *     tags: [Guardians]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, approved, rejected, suspended]
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           default: created_at
 *       - in: query
 *         name: order
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: DESC
 *     responses:
 *       200:
 *         description: Guardians retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.get('/', authenticate, requireGuardianOrAdmin, getGuardians);

/**
 * @swagger
 * /api/guardians/{id}:
 *   get:
 *     summary: Get guardian by ID
 *     tags: [Guardians]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Guardian retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Guardian not found
 */
router.get('/:id', authenticate, requireGuardianOrAdmin, getGuardianById);

/**
 * @swagger
 * /api/guardians/{id}/approve:
 *   patch:
 *     summary: Approve guardian application (Admin only)
 *     tags: [Guardians]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Guardian approved successfully
 *       400:
 *         description: Guardian application is not pending
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Guardian not found
 */
router.patch('/:id/approve', authenticate, requireAdmin, approveGuardian);

/**
 * @swagger
 * /api/guardians/{id}/reject:
 *   patch:
 *     summary: Reject guardian application (Admin only)
 *     tags: [Guardians]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *                 maxLength: 500
 *     responses:
 *       200:
 *         description: Guardian application rejected
 *       400:
 *         description: Guardian application is not pending
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Guardian not found
 */
router.patch('/:id/reject', authenticate, requireAdmin, validate(guardianActionSchema), rejectGuardian);

/**
 * @swagger
 * /api/guardians/{id}/revoke:
 *   patch:
 *     summary: Revoke guardian access (Admin only)
 *     tags: [Guardians]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *                 maxLength: 500
 *     responses:
 *       200:
 *         description: Guardian access revoked successfully
 *       400:
 *         description: Guardian is not currently approved
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Guardian not found
 */
router.patch('/:id/revoke', authenticate, requireAdmin, validate(guardianActionSchema), revokeGuardian);

export default router;
