import { Response } from 'express';
import { Op } from 'sequelize';
import { Incident, User, Report, Role } from '../models';
import { CustomError, asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest, ApiResponse, PaginatedResponse } from '../types';

export const createIncident = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const {
    title,
    description,
    platform,
    category,
    severity = 'medium',
    url,
    evidenceUrls = [],
    isAnonymous = false,
    reporterContact,
    location,
  } = req.body;

  const incidentData: any = {
    title,
    description,
    platform,
    category,
    severity,
    url,
    evidence_urls: evidenceUrls,
    is_anonymous: isAnonymous,
    location,
    status: 'open',
  };

  if (isAnonymous) {
    if (!reporterContact) {
      throw new CustomError('Reporter contact is required for anonymous reports', 400);
    }
    incidentData.reporter_contact = reporterContact;
  } else {
    if (!req.user) {
      throw new CustomError('Authentication required for non-anonymous reports', 401);
    }
    incidentData.reporter_id = req.user.id;
  }

  const incident = await Incident.create(incidentData);

  // Fetch the created incident with relations
  const createdIncident = await Incident.findByPk(incident.id, {
    include: [
      {
        model: User,
        as: 'reporter',
        attributes: ['id', 'name', 'email'],
        required: false,
      },
    ],
  });

  const response: ApiResponse = {
    success: true,
    message: 'Incident reported successfully',
    data: {
      incident: {
        id: createdIncident!.id,
        title: createdIncident!.title,
        description: createdIncident!.description,
        platform: createdIncident!.platform,
        category: createdIncident!.category,
        status: createdIncident!.status,
        severity: createdIncident!.severity,
        url: createdIncident!.url,
        evidenceUrls: createdIncident!.evidence_urls,
        isAnonymous: createdIncident!.is_anonymous,
        reporterContact: createdIncident!.reporter_contact,
        location: createdIncident!.location,
        reporter: createdIncident!.reporter,
        createdAt: createdIncident!.created_at,
      },
    },
  };

  res.status(201).json(response);
});

export const getIncidents = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const {
    page = '1',
    limit = '10',
    search = '',
    status = '',
    category = '',
    platform = '',
    severity = '',
    sort = 'created_at',
    order = 'DESC',
    my = 'false', // Filter for user's own incidents
  } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  // Build where clause
  const whereClause: any = {};
  
  if (status && status !== 'all') {
    whereClause.status = status;
  }
  
  if (category && category !== 'all') {
    whereClause.category = category;
  }
  
  if (platform && platform !== 'all') {
    whereClause.platform = platform;
  }
  
  if (severity && severity !== 'all') {
    whereClause.severity = severity;
  }

  if (search) {
    whereClause[Op.or] = [
      { title: { [Op.like]: `%${search}%` } },
      { description: { [Op.like]: `%${search}%` } },
    ];
  }

  // Filter for user's own incidents if requested
  if (my === 'true' && req.user) {
    whereClause.reporter_id = req.user.id;
  }

  // Non-admin users can only see non-anonymous incidents or their own
  if (req.user?.role !== 'admin') {
    if (req.user) {
      whereClause[Op.or] = [
        { is_anonymous: false },
        { reporter_id: req.user.id },
      ];
    } else {
      whereClause.is_anonymous = false;
    }
  }

  const { count, rows: incidents } = await Incident.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'reporter',
        attributes: ['id', 'name', 'email'],
        required: false,
      },
      {
        model: User,
        as: 'guardian',
        attributes: ['id', 'name', 'email'],
        required: false,
      },
    ],
    limit: limitNum,
    offset,
    order: [[sort as string, order as string]],
  });

  const totalPages = Math.ceil(count / limitNum);

  const response: PaginatedResponse<any> = {
    data: incidents.map(incident => ({
      id: incident.id,
      title: incident.title,
      description: incident.description,
      platform: incident.platform,
      category: incident.category,
      status: incident.status,
      severity: incident.severity,
      url: incident.url,
      evidenceUrls: incident.evidence_urls,
      isAnonymous: incident.is_anonymous,
      reporterContact: incident.is_anonymous ? incident.reporter_contact : undefined,
      location: incident.location,
      reporter: incident.reporter,
      guardian: incident.guardian,
      createdAt: incident.created_at,
      updatedAt: incident.updated_at,
    })),
    pagination: {
      page: pageNum,
      limit: limitNum,
      total: count,
      totalPages,
      hasNext: pageNum < totalPages,
      hasPrev: pageNum > 1,
    },
  };

  res.status(200).json({
    success: true,
    ...response,
  });
});

export const getIncidentById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const incident = await Incident.findByPk(id, {
    include: [
      {
        model: User,
        as: 'reporter',
        attributes: ['id', 'name', 'email'],
        required: false,
      },
      {
        model: User,
        as: 'guardian',
        attributes: ['id', 'name', 'email'],
        required: false,
      },
      {
        model: Report,
        as: 'reports',
        include: [
          {
            model: User,
            as: 'reporter',
            attributes: ['id', 'name', 'email'],
            required: false,
          },
          {
            model: User,
            as: 'guardian',
            attributes: ['id', 'name', 'email'],
            required: false,
          },
        ],
        order: [['created_at', 'ASC']],
      },
    ],
  });

  if (!incident) {
    throw new CustomError('Incident not found', 404);
  }

  // Check access permissions
  if (req.user?.role !== 'admin' && req.user?.role !== 'guardian') {
    if (incident.is_anonymous || (incident.reporter_id && incident.reporter_id !== req.user?.id)) {
      throw new CustomError('Access denied', 403);
    }
  }

  const response: ApiResponse = {
    success: true,
    data: {
      incident: {
        id: incident.id,
        title: incident.title,
        description: incident.description,
        platform: incident.platform,
        category: incident.category,
        status: incident.status,
        severity: incident.severity,
        url: incident.url,
        evidenceUrls: incident.evidence_urls,
        isAnonymous: incident.is_anonymous,
        reporterContact: incident.is_anonymous ? incident.reporter_contact : undefined,
        location: incident.location,
        reporter: incident.reporter,
        guardian: incident.guardian,
        reports: incident.reports?.filter(report =>
          !report.is_internal || req.user?.role === 'admin' || req.user?.role === 'guardian'
        ),
        createdAt: incident.created_at,
        updatedAt: incident.updated_at,
      },
    },
  };

  res.status(200).json(response);
});

export const updateIncidentStatus = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { status, guardianId } = req.body;

  const incident = await Incident.findByPk(id);
  if (!incident) {
    throw new CustomError('Incident not found', 404);
  }

  // Only guardians and admins can update incident status
  if (req.user?.role !== 'admin' && req.user?.role !== 'guardian') {
    throw new CustomError('Insufficient permissions', 403);
  }

  const updateData: any = { status };

  // Assign guardian if provided
  if (guardianId) {
    const guardian = await User.findByPk(guardianId, {
      include: [{ model: Role, as: 'role', where: { name: 'guardian' } }],
    });

    if (!guardian) {
      throw new CustomError('Guardian not found', 404);
    }

    updateData.guardian_id = guardianId;
  }

  await incident.update(updateData);

  // Fetch updated incident
  const updatedIncident = await Incident.findByPk(id, {
    include: [
      {
        model: User,
        as: 'reporter',
        attributes: ['id', 'name', 'email'],
        required: false,
      },
      {
        model: User,
        as: 'guardian',
        attributes: ['id', 'name', 'email'],
        required: false,
      },
    ],
  });

  const response: ApiResponse = {
    success: true,
    message: 'Incident status updated successfully',
    data: {
      incident: {
        id: updatedIncident!.id,
        status: updatedIncident!.status,
        guardian: updatedIncident!.guardian,
        updatedAt: updatedIncident!.updated_at,
      },
    },
  };

  res.status(200).json(response);
});

export const deleteIncident = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const incident = await Incident.findByPk(id);
  if (!incident) {
    throw new CustomError('Incident not found', 404);
  }

  // Only admin or the reporter can delete an incident
  if (req.user?.role !== 'admin' && incident.reporter_id !== req.user?.id) {
    throw new CustomError('Insufficient permissions', 403);
  }

  await incident.destroy();

  const response: ApiResponse = {
    success: true,
    message: 'Incident deleted successfully',
  };

  res.status(200).json(response);
});
