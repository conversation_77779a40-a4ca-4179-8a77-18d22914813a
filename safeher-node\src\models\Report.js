const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class Report extends Model {
  // Associations
  static associate(models) {
    Report.belongsTo(models.Incident, {
      foreignKey: 'incident_id',
      as: 'incident'
    });

    Report.belongsTo(models.User, {
      foreignKey: 'reporter_id',
      as: 'reporter'
    });

    Report.belongsTo(models.User, {
      foreignKey: 'guardian_id',
      as: 'guardian'
    });
  }
}

Report.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    incident_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'incidents',
        key: 'id',
      },
    },
    reporter_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    guardian_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        len: [1, 5000],
        notEmpty: true,
      },
    },
    type: {
      type: DataTypes.ENUM('update', 'comment', 'evidence', 'resolution'),
      allowNull: false,
      validate: {
        isIn: [['update', 'comment', 'evidence', 'resolution']],
      },
    },
    is_internal: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    attachments: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'Report',
    tableName: 'reports',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['incident_id'],
      },
      {
        fields: ['reporter_id'],
      },
      {
        fields: ['guardian_id'],
      },
      {
        fields: ['type'],
      },
      {
        fields: ['is_internal'],
      },
      {
        fields: ['created_at'],
      },
    ],
    validate: {
      reporterValidation() {
        if (!this.reporter_id && !this.guardian_id) {
          throw new Error('Report must have either a reporter_id or guardian_id');
        }
      },
    },
  }
);

module.exports = Report;
