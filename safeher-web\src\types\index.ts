// User Types
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'guardian' | 'user';
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Guardian Types
export interface Guardian {
  id: string;
  name: string;
  email: string;
  designation: string;
  resourcesCount: number;
  reportsCount: number;
  joinedDate: Date;
  avatar?: string;
}

// Incident Types
export interface Incident {
  id: string;
  title: string;
  description: string;
  platform: 'facebook' | 'instagram' | 'twitter' | 'tiktok' | 'other';
  category: 'body-shaming' | 'harassment' | 'cyberbullying' | 'other';
  status: 'pending' | 'investigating' | 'resolved' | 'closed';
  reportedBy: string;
  reportedAt: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Resource Types
export interface Resource {
  id: string;
  title: string;
  description: string;
  category: 'guide' | 'article' | 'video' | 'tool';
  content: string;
  author: string;
  publishedAt: Date;
  tags: string[];
  featured: boolean;
}

// Dashboard Stats
export interface DashboardStats {
  totalIncidents: number;
  totalGuardians: number;
  totalUsers: number;
  totalReports: number;
  incidentsOverTime: {
    date: string;
    facebook: number;
    instagram: number;
  }[];
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
