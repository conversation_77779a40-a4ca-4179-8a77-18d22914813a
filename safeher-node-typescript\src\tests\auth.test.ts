import request from 'supertest';
import app from '../index';
import { TestDataFactory, expectSuccess, expectValidationError, expectUnauthorized } from './helpers/testUtils';

describe('Authentication Endpoints', () => {
  beforeEach(async () => {
    await TestDataFactory.createRoles();
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'Password123!',
        confirmPassword: 'Password123!',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      expectSuccess(response, 201);
      expect(response.body.data.user).toHaveProperty('id');
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.user.name).toBe(userData.name);
      expect(response.body.data.user.role).toBe('user');
      expect(response.body.data.tokens).toHaveProperty('accessToken');
      expect(response.body.data.tokens).toHaveProperty('refreshToken');
    });

    it('should fail with invalid email', async () => {
      const userData = {
        name: 'John Doe',
        email: 'invalid-email',
        password: 'Password123!',
        confirmPassword: 'Password123!',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      expectValidationError(response, 'email');
    });

    it('should fail with weak password', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'weak',
        confirmPassword: 'weak',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      expectValidationError(response, 'password');
    });

    it('should fail with mismatched passwords', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'Password123!',
        confirmPassword: 'DifferentPassword123!',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      expectValidationError(response, 'match');
    });

    it('should fail with duplicate email', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'Password123!',
        confirmPassword: 'Password123!',
      };

      // First registration
      await request(app)
        .post('/api/auth/register')
        .send(userData);

      // Second registration with same email
      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      expect(response.status).toBe(409);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('already exists');
    });
  });

  describe('POST /api/auth/login', () => {
    it('should login successfully with valid credentials', async () => {
      // Create a user first
      const user = await TestDataFactory.createUser({
        email: '<EMAIL>',
        password: 'Password123!',
      });

      const loginData = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      expectSuccess(response);
      expect(response.body.data.user).toHaveProperty('id');
      expect(response.body.data.user.email).toBe(loginData.email);
      expect(response.body.data.tokens).toHaveProperty('accessToken');
      expect(response.body.data.tokens).toHaveProperty('refreshToken');
    });

    it('should fail with invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      expectUnauthorized(response);
    });

    it('should fail with invalid password', async () => {
      // Create a user first
      await TestDataFactory.createUser({
        email: '<EMAIL>',
        password: 'Password123!',
      });

      const loginData = {
        email: '<EMAIL>',
        password: 'WrongPassword123!',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      expectUnauthorized(response);
    });

    it('should fail with inactive user', async () => {
      // Create an inactive user
      await TestDataFactory.createUser({
        email: '<EMAIL>',
        password: 'Password123!',
        isActive: false,
      });

      const loginData = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      expectUnauthorized(response);
    });
  });

  describe('POST /api/auth/register-guardian', () => {
    it('should register guardian application successfully', async () => {
      const guardianData = {
        name: 'Dr. Jane Smith',
        email: '<EMAIL>',
        password: 'Password123!',
        confirmPassword: 'Password123!',
        designation: 'Cybersecurity Specialist',
        organization: 'Women Safety Foundation',
        bio: 'Experienced in online safety',
        expertiseAreas: ['cybersecurity', 'legal'],
      };

      const response = await request(app)
        .post('/api/auth/register-guardian')
        .send(guardianData);

      expectSuccess(response, 201);
      expect(response.body.data.user.role).toBe('user'); // Starts as user until approved
      expect(response.body.data.guardian.status).toBe('pending');
      expect(response.body.data.guardian.designation).toBe(guardianData.designation);
    });

    it('should fail without required designation', async () => {
      const guardianData = {
        name: 'Dr. Jane Smith',
        email: '<EMAIL>',
        password: 'Password123!',
        confirmPassword: 'Password123!',
        // Missing designation
        organization: 'Women Safety Foundation',
      };

      const response = await request(app)
        .post('/api/auth/register-guardian')
        .send(guardianData);

      expectValidationError(response, 'designation');
    });
  });

  describe('GET /api/auth/profile', () => {
    it('should get user profile successfully', async () => {
      const user = await TestDataFactory.createUser();

      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${user.token}`);

      expectSuccess(response);
      expect(response.body.data.user.id).toBe(user.id);
      expect(response.body.data.user.email).toBe(user.email);
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .get('/api/auth/profile');

      expectUnauthorized(response);
    });

    it('should fail with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token');

      expectUnauthorized(response);
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout successfully', async () => {
      const user = await TestDataFactory.createUser();

      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${user.token}`);

      expectSuccess(response);
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .post('/api/auth/logout');

      expectUnauthorized(response);
    });
  });
});
