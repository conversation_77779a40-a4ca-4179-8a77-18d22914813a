'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('guardians', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.INTEGER,
        autoIncrement: true
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        unique: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      designation: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      organization: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      bio: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      expertise_areas: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: '[]'
      },
      status: {
        type: Sequelize.ENUM('pending', 'approved', 'rejected', 'suspended'),
        allowNull: false,
        defaultValue: 'pending'
      },
      resources_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      reports_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      approved_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      approved_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('guardians', ['user_id']);
    await queryInterface.addIndex('guardians', ['status']);
    await queryInterface.addIndex('guardians', ['approved_by']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('guardians');
  }
};
