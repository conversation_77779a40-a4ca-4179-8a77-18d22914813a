import Link from 'next/link';
import Button from '@/components/ui/Button';

export default function HeroSection() {
  return (
    <section className="bg-white py-16 lg:py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <p className="text-sm font-medium text-gray-600 uppercase tracking-wide">
                WELCOME TO SAFEHER
              </p>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                "Empowering Women: Your Safe Haven Against Online Violence"
              </h1>
            </div>
            
            <p className="text-lg text-gray-600 leading-relaxed">
              SafeHer by SafeOnline Women Kenya is a pioneering mobile app specifically 
              designed to combat online violence against women. With its innovative 
              features, community-driven reporting system, and commitment to education, 
              SafeHer empowers women to navigate the online landscape with confidence, 
              fostering a safer and more inclusive digital environment.
            </p>

            {/* Download Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link 
                href="#" 
                className="flex items-center justify-center space-x-3 bg-gray-900 text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors"
              >
                <span className="text-2xl">▶</span>
                <div className="text-left">
                  <p className="text-xs">Download</p>
                  <p className="font-semibold">Play store</p>
                </div>
              </Link>
              
              <Link 
                href="#" 
                className="flex items-center justify-center space-x-3 bg-gray-900 text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors"
              >
                <span className="text-2xl">🍎</span>
                <div className="text-left">
                  <p className="text-xs">Download</p>
                  <p className="font-semibold">App store</p>
                </div>
              </Link>
            </div>
          </div>

          {/* Right Content - Placeholder for app mockup */}
          <div className="relative">
            <div className="bg-gradient-to-br from-pink-100 to-purple-100 rounded-2xl p-8 h-96 flex items-center justify-center">
              <div className="text-center text-gray-500">
                <div className="w-32 h-32 bg-white rounded-2xl shadow-lg mx-auto mb-4 flex items-center justify-center">
                  <span className="text-4xl">📱</span>
                </div>
                <p className="font-medium">SafeHer Mobile App</p>
                <p className="text-sm">Coming Soon</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
