import { Router } from 'express';
import {
  createResource,
  getResources,
  getResourceById,
  updateResource,
  deleteResource,
  likeResource,
} from '../controllers/resourceController';
import { authenticate, optionalAuth, requireGuardianOrAdmin } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { z } from 'zod';

const router = Router();

// Validation schemas
const createResourceSchema = z.object({
  body: z.object({
    title: z.string().min(5).max(255).trim(),
    description: z.string().min(10).max(500).trim(),
    content: z.string().min(50).max(10000).trim(),
    category: z.enum(['legal', 'safety', 'mental_health', 'technical', 'educational', 'other']),
    tags: z.array(z.string().trim()).max(10).default([]),
    attachmentUrls: z.array(z.string().url()).max(5).default([]),
    isPublished: z.boolean().default(false),
  }),
});

const updateResourceSchema = z.object({
  body: z.object({
    title: z.string().min(5).max(255).trim().optional(),
    description: z.string().min(10).max(500).trim().optional(),
    content: z.string().min(50).max(10000).trim().optional(),
    category: z.enum(['legal', 'safety', 'mental_health', 'technical', 'educational', 'other']).optional(),
    tags: z.array(z.string().trim()).max(10).optional(),
    attachmentUrls: z.array(z.string().url()).max(5).optional(),
    isPublished: z.boolean().optional(),
  }),
});

/**
 * @swagger
 * /api/resources:
 *   post:
 *     summary: Create a new resource (Guardian/Admin only)
 *     tags: [Resources]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *               - content
 *               - category
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 5
 *                 maxLength: 255
 *               description:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 500
 *               content:
 *                 type: string
 *                 minLength: 50
 *                 maxLength: 10000
 *               category:
 *                 type: string
 *                 enum: [legal, safety, mental_health, technical, educational, other]
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 maxItems: 10
 *               attachmentUrls:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uri
 *                 maxItems: 5
 *               isPublished:
 *                 type: boolean
 *                 default: false
 *     responses:
 *       201:
 *         description: Resource created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.post('/', authenticate, requireGuardianOrAdmin, validate(createResourceSchema), createResource);

/**
 * @swagger
 * /api/resources:
 *   get:
 *     summary: Get paginated list of resources
 *     tags: [Resources]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *       - in: query
 *         name: tags
 *         schema:
 *           type: string
 *           description: Comma-separated list of tags
 *       - in: query
 *         name: published
 *         schema:
 *           type: boolean
 *           default: true
 *       - in: query
 *         name: my
 *         schema:
 *           type: boolean
 *           default: false
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           default: created_at
 *       - in: query
 *         name: order
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: DESC
 *     responses:
 *       200:
 *         description: Resources retrieved successfully
 */
router.get('/', optionalAuth, getResources);

/**
 * @swagger
 * /api/resources/{id}:
 *   get:
 *     summary: Get resource by ID
 *     tags: [Resources]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Resource retrieved successfully
 *       404:
 *         description: Resource not found
 */
router.get('/:id', optionalAuth, getResourceById);

/**
 * @swagger
 * /api/resources/{id}:
 *   put:
 *     summary: Update resource (Author or Admin only)
 *     tags: [Resources]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 5
 *                 maxLength: 255
 *               description:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 500
 *               content:
 *                 type: string
 *                 minLength: 50
 *                 maxLength: 10000
 *               category:
 *                 type: string
 *                 enum: [legal, safety, mental_health, technical, educational, other]
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 maxItems: 10
 *               attachmentUrls:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uri
 *                 maxItems: 5
 *               isPublished:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Resource updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Resource not found
 */
router.put('/:id', authenticate, validate(updateResourceSchema), updateResource);

/**
 * @swagger
 * /api/resources/{id}:
 *   delete:
 *     summary: Delete resource (Author or Admin only)
 *     tags: [Resources]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Resource deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Resource not found
 */
router.delete('/:id', authenticate, deleteResource);

/**
 * @swagger
 * /api/resources/{id}/like:
 *   post:
 *     summary: Like a resource
 *     tags: [Resources]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Resource liked successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Resource not found
 */
router.post('/:id/like', authenticate, likeResource);

export default router;
