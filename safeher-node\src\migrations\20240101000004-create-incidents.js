'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('incidents', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4
      },
      title: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      platform: {
        type: Sequelize.ENUM('facebook', 'instagram', 'twitter', 'tiktok', 'whatsapp', 'telegram', 'other'),
        allowNull: false
      },
      category: {
        type: Sequelize.ENUM('body-shaming', 'harassment', 'cyberbullying', 'stalking', 'threats', 'doxxing', 'other'),
        allowNull: false
      },
      status: {
        type: Sequelize.ENUM('open', 'in_review', 'resolved', 'closed'),
        allowNull: false,
        defaultValue: 'open'
      },
      severity: {
        type: Sequelize.ENUM('low', 'medium', 'high', 'critical'),
        allowNull: false,
        defaultValue: 'medium'
      },
      url: {
        type: Sequelize.STRING(500),
        allowNull: true
      },
      evidence_urls: {
        type: Sequelize.JSON,
        allowNull: true,
        defaultValue: []
      },
      is_anonymous: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      reporter_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      reporter_contact: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      guardian_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      location: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('incidents', ['reporter_id']);
    await queryInterface.addIndex('incidents', ['guardian_id']);
    await queryInterface.addIndex('incidents', ['status']);
    await queryInterface.addIndex('incidents', ['category']);
    await queryInterface.addIndex('incidents', ['platform']);
    await queryInterface.addIndex('incidents', ['severity']);
    await queryInterface.addIndex('incidents', ['is_anonymous']);
    await queryInterface.addIndex('incidents', ['created_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('incidents');
  }
};
