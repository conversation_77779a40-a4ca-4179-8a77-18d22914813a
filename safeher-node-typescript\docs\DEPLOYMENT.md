# SafeHer Backend Deployment Guide

This guide covers deploying the SafeHer backend API to various environments.

## 🚀 Production Deployment

### Prerequisites

- Node.js 18+ installed on the server
- MySQL 8.0+ database server
- Process manager (PM2 recommended)
- Reverse proxy (Nginx recommended)
- SSL certificate for HTTPS

### Environment Setup

1. **Create production environment file**
   ```bash
   cp .env.example .env.production
   ```

2. **Configure production variables**
   ```env
   NODE_ENV=production
   
   # Database Configuration
   DB_HOST=your_production_db_host
   DB_PORT=3306
   DB_NAME=safeher_production
   DB_USER=safeher_user
   DB_PASSWORD=your_secure_password
   
   # JWT Configuration (Use strong, unique secrets)
   JWT_SECRET=your_super_secure_jwt_secret_key_min_32_chars
   JWT_REFRESH_SECRET=your_super_secure_refresh_secret_key_min_32_chars
   JWT_EXPIRES_IN=15m
   JWT_REFRESH_EXPIRES_IN=7d
   
   # API Configuration
   PORT=3001
   API_URL=https://api.safeher.com
   FRONTEND_URL=https://safeher.com
   
   # Security Configuration
   RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
   RATE_LIMIT_MAX_REQUESTS=100
   
   # File Upload Configuration
   UPLOAD_DIR=./uploads/production
   MAX_FILE_SIZE=10485760  # 10MB
   
   # Email Configuration (Production SMTP)
   EMAIL_SERVICE=smtp
   EMAIL_HOST=smtp.your-provider.com
   EMAIL_PORT=587
   EMAIL_SECURE=false
   EMAIL_USER=<EMAIL>
   EMAIL_PASSWORD=your_email_password
   EMAIL_FROM=SafeHer <<EMAIL>>
   
   # Logging
   LOG_LEVEL=info
   ```

### Database Setup

1. **Create production database**
   ```bash
   mysql -u root -p
   CREATE DATABASE safeher_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'safeher_user'@'localhost' IDENTIFIED BY 'your_secure_password';
   GRANT ALL PRIVILEGES ON safeher_production.* TO 'safeher_user'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;
   ```

2. **Run migrations and seeders**
   ```bash
   NODE_ENV=production npm run db:migrate
   NODE_ENV=production npm run db:seed
   ```

### Application Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Install PM2 globally**
   ```bash
   npm install -g pm2
   ```

3. **Create PM2 ecosystem file**
   ```javascript
   // ecosystem.config.js
   module.exports = {
     apps: [{
       name: 'safeher-api',
       script: './dist/index.js',
       instances: 'max',
       exec_mode: 'cluster',
       env: {
         NODE_ENV: 'production',
         PORT: 3001
       },
       error_file: './logs/err.log',
       out_file: './logs/out.log',
       log_file: './logs/combined.log',
       time: true,
       max_memory_restart: '1G',
       node_args: '--max_old_space_size=1024'
     }]
   };
   ```

4. **Start the application with PM2**
   ```bash
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

### Nginx Configuration

1. **Install Nginx**
   ```bash
   sudo apt update
   sudo apt install nginx
   ```

2. **Create Nginx configuration**
   ```nginx
   # /etc/nginx/sites-available/safeher-api
   server {
       listen 80;
       server_name api.safeher.com;
       
       # Redirect HTTP to HTTPS
       return 301 https://$server_name$request_uri;
   }
   
   server {
       listen 443 ssl http2;
       server_name api.safeher.com;
       
       # SSL Configuration
       ssl_certificate /path/to/your/certificate.crt;
       ssl_certificate_key /path/to/your/private.key;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
       ssl_prefer_server_ciphers off;
       
       # Security Headers
       add_header X-Frame-Options DENY;
       add_header X-Content-Type-Options nosniff;
       add_header X-XSS-Protection "1; mode=block";
       add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
       
       # Rate Limiting
       limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
       limit_req zone=api burst=20 nodelay;
       
       # Proxy Configuration
       location / {
           proxy_pass http://localhost:3001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
           
           # Timeouts
           proxy_connect_timeout 60s;
           proxy_send_timeout 60s;
           proxy_read_timeout 60s;
       }
       
       # File Upload Size
       client_max_body_size 10M;
       
       # Logging
       access_log /var/log/nginx/safeher-api.access.log;
       error_log /var/log/nginx/safeher-api.error.log;
   }
   ```

3. **Enable the site**
   ```bash
   sudo ln -s /etc/nginx/sites-available/safeher-api /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl reload nginx
   ```

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
# Dockerfile
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S safeher -u 1001

# Change ownership
RUN chown -R safeher:nodejs /app
USER safeher

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# Start application
CMD ["npm", "start"]
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    depends_on:
      - mysql
    restart: unless-stopped
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    restart: unless-stopped

volumes:
  mysql_data:
```

### Deploy with Docker

```bash
# Build and start services
docker-compose up -d

# Run migrations
docker-compose exec api npm run db:migrate

# Run seeders
docker-compose exec api npm run db:seed
```

## ☁️ Cloud Deployment

### AWS Deployment

1. **EC2 Instance Setup**
   - Launch EC2 instance (t3.medium or larger)
   - Configure security groups (ports 22, 80, 443)
   - Install Node.js, MySQL, Nginx

2. **RDS Database**
   - Create RDS MySQL instance
   - Configure security groups
   - Update connection string in environment

3. **Application Load Balancer**
   - Create ALB with SSL certificate
   - Configure target groups
   - Set up health checks

### Heroku Deployment

1. **Prepare for Heroku**
   ```bash
   # Install Heroku CLI
   npm install -g heroku
   
   # Login to Heroku
   heroku login
   
   # Create Heroku app
   heroku create safeher-api
   ```

2. **Configure environment variables**
   ```bash
   heroku config:set NODE_ENV=production
   heroku config:set JWT_SECRET=your_jwt_secret
   # ... other environment variables
   ```

3. **Add MySQL addon**
   ```bash
   heroku addons:create jawsdb:kitefin
   ```

4. **Deploy**
   ```bash
   git push heroku main
   heroku run npm run db:migrate
   heroku run npm run db:seed
   ```

## 🔧 Monitoring and Maintenance

### Health Monitoring

1. **PM2 Monitoring**
   ```bash
   pm2 monit
   pm2 logs safeher-api
   ```

2. **Database Monitoring**
   ```bash
   # Check MySQL status
   sudo systemctl status mysql
   
   # Monitor connections
   mysql -u root -p -e "SHOW PROCESSLIST;"
   ```

### Backup Strategy

1. **Database Backups**
   ```bash
   # Create backup script
   #!/bin/bash
   DATE=$(date +%Y%m%d_%H%M%S)
   mysqldump -u safeher_user -p safeher_production > backup_$DATE.sql
   
   # Schedule with cron
   0 2 * * * /path/to/backup-script.sh
   ```

2. **File Backups**
   ```bash
   # Backup uploads directory
   tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/
   ```

### Log Management

1. **Log Rotation**
   ```bash
   # Configure logrotate
   sudo nano /etc/logrotate.d/safeher-api
   ```

2. **Centralized Logging**
   - Consider using ELK stack or cloud logging services
   - Monitor application logs and error rates

## 🚨 Security Checklist

- [ ] Use HTTPS with valid SSL certificates
- [ ] Configure strong JWT secrets
- [ ] Enable rate limiting
- [ ] Set up firewall rules
- [ ] Regular security updates
- [ ] Database access restrictions
- [ ] Environment variable security
- [ ] Regular backups
- [ ] Monitor for vulnerabilities
- [ ] Implement proper logging

## 📊 Performance Optimization

1. **Database Optimization**
   - Add proper indexes
   - Optimize queries
   - Connection pooling

2. **Caching**
   - Implement Redis for session storage
   - Cache frequently accessed data

3. **Load Balancing**
   - Use multiple application instances
   - Implement sticky sessions if needed

4. **CDN**
   - Use CDN for static assets
   - Optimize file uploads

---

For additional support, refer to the main README or contact the development team.
