<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Admin Guardians – Table Layout</title>
  <style>
    * { box-sizing: border-box; margin: 0; padding: 0; }
    body { font-family: 'Montserrat', sans-serif; background: #F5F7FA; color: #21264D; }
    .container { display: flex; min-height: 100vh; }

    /* ——— Side Nav (same as before) ——— */
    .sidenav {
      width: 220px;
      background: #FFFFFF;
      border-right: 1px solid #EAECF0;
      padding: 32px 16px;
    }
    .nav-list { list-style: none; }
    .nav-list li {
      margin-bottom: 24px;
      font-weight: 600;
      color: #4D5171;
      cursor: pointer;
    }
    .nav-list li.active { color: #CF1884; }

    /* ——— Main Content ——— */
    .main {
      flex: 1;
      padding: 32px;
      display: flex;
      flex-direction: column;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }
    .header h1 { font-size: 24px; font-weight: 700; }

    .search-filter {
      display: flex;
      gap: 8px;
    }
    .search-filter input {
      padding: 8px 12px;
      border: 1px solid #EAECF0;
      border-radius: 8px;
      width: 240px;
    }
    .search-filter button {
      padding: 8px 16px;
      border: 1px solid #CF1884;
      background: #FFFFFF;
      color: #CF1884;
      border-radius: 8px;
      cursor: pointer;
    }

    /* ——— Table Styles ——— */
    .table-container { overflow-x: auto; }
    table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0 8px;
    }
    thead th {
      text-align: left;
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 600;
      color: #4D5171;
    }
    tbody tr {
      background: #F2F4F7;
      border-radius: 8px;
      overflow: hidden;
      display: table;
      width: 100%;
      table-layout: fixed;
    }
    tbody td {
      padding: 16px;
      vertical-align: middle;
      font-size: 14px;
      color: #2B1A55;
    }
    tbody td + td { text-align: center; }
    tbody td:last-child { text-align: right; }

    .guardian-cell {
      display: flex;
      align-items: center;
      gap: 12px;
    }
    .guardian-cell img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
    }
    .guardian-info h2 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }
    .guardian-info p {
      font-size: 12px;
      color: #706390;
    }

    /* ——— Pagination ——— */
    .pagination {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 24px;
    }
    .pagination .pages {
      display: flex;
      gap: 8px;
      list-style: none;
    }
    .pagination button,
    .pagination li {
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 12px;
      font-weight: 700;
      cursor: pointer;
      border: 1px solid #EAECF0;
      background: #FFFFFF;
      color: #21264D;
    }
    .pagination li.active { background: #FFFFFF; color: #2B1A55; border-color: #EAECF0; }
    .pagination button:hover,
    .pagination li:hover { background: #F2F4F7; }
  </style>
</head>
<body>

  <div class="container">
    <nav class="sidenav">
      <ul class="nav-list">
        <li>Dashboard</li>
        <li>Incidences</li>
        <li>Resources</li>
        <li class="active">Guardians</li>
        <li>Users</li>
        <li>Settings</li>
        <li>Log out</li>
      </ul>
    </nav>

    <main class="main">
      <div class="header">
        <h1>Guardians</h1>
        <div class="search-filter">
          <input type="text" placeholder="Search…" />
          <button>Filter</button>
        </div>
      </div>

      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th>Guardian</th>
              <th>Email</th>
              <th>Resources</th>
              <th>Reports</th>
              <th>Designation</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="guardian-cell">
                <img src="https://i.pravatar.cc/40?img=5" alt="" />
                <div class="guardian-info">
                  <h2>Joshua Silverman</h2>
                  <p>Since 19/10/2022</p>
                </div>
              </td>
              <td><EMAIL></td>
              <td>34</td>
              <td>400</td>
              <td>Volunteer</td>
            </tr>
            <tr>
              <td class="guardian-cell">
                <img src="https://i.pravatar.cc/40?img=12" alt="" />
                <div class="guardian-info">
                  <h2>Olivia Orero</h2>
                  <p>Since 19/10/2022</p>
                </div>
              </td>
              <td><EMAIL></td>
              <td>40</td>
              <td>250</td>
              <td>Sow Kenya</td>
            </tr>
            <tr>
              <td class="guardian-cell">
                <img src="https://i.pravatar.cc/40?img=15" alt="" />
                <div class="guardian-info">
                  <h2>David Mburu</h2>
                  <p>Since 19/10/2022</p>
                </div>
              </td>
              <td><EMAIL></td>
              <td>40</td>
              <td>400</td>
              <td>Sow Kenya</td>
            </tr>
            <tr>
              <td class="guardian-cell">
                <img src="https://i.pravatar.cc/40?img=20" alt="" />
                <div class="guardian-info">
                  <h2>Olive Musyoka</h2>
                  <p>Since 19/10/2022</p>
                </div>
              </td>
              <td><EMAIL></td>
              <td>40</td>
              <td>450</td>
              <td>Safewomen</td>
            </tr>
            <tr>
              <td class="guardian-cell">
                <img src="https://i.pravatar.cc/40?img=8" alt="" />
                <div class="guardian-info">
                  <h2>Edwin Murimi</h2>
                  <p>Since 19/10/2022</p>
                </div>
              </td>
              <td><EMAIL></td>
              <td>40</td>
              <td>600</td>
              <td>Volunteer</td>
            </tr>
            <tr>
              <td class="guardian-cell">
                <img src="https://i.pravatar.cc/40?img=32" alt="" />
                <div class="guardian-info">
                  <h2>Oscar Tiego</h2>
                  <p>Since 19/10/2022</p>
                </div>
              </td>
              <td><EMAIL></td>
              <td>40</td>
              <td>60</td>
              <td>Volunteer</td>
            </tr>
            <tr>
              <td class="guardian-cell">
                <img src="https://i.pravatar.cc/40?img=45" alt="" />
                <div class="guardian-info">
                  <h2>Andy Young</h2>
                  <p>Since 19/10/2022</p>
                </div>
              </td>
              <td><EMAIL></td>
              <td>40</td>
              <td>650</td>
              <td>Volunteer</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="pagination">
        <button>Previous</button>
        <ul class="pages">
          <li>1</li>
          <li class="active">2</li>
          <li>3</li>
          <li>…</li>
          <li>10</li>
        </ul>
        <button>Next</button>
      </div>
    </main>
  </div>
</body>
</html>
