'use client';

import { useState } from 'react';
import { EllipsisHorizontalIcon, XMarkIcon, ChatBubbleLeftIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';
import { getTypography, createCardVariant } from '@/lib/design-tokens';

// Mock data for reports
const mockReports = [
  {
    id: 1,
    author: 'Safeher',
    isAnonymous: false,
    avatar: '/api/placeholder/40/40',
    content: 'This is a case of a video where the person in the video is seen body shaming women while also...',
    image: '/api/placeholder/300/200',
    url: 'https://www.safeher.com/body-shaming-case-url-here',
    timestamp: '2 hrs ago',
  },
  {
    id: 2,
    author: 'Anonymous',
    isAnonymous: true,
    avatar: '/api/placeholder/40/40',
    content: 'This is a case of a video where the person in the video is seen body shaming women while also...',
    image: '/api/placeholder/300/200',
    url: 'https://www.safeher.com/body-shaming-case-url-here',
    timestamp: '4 hrs ago',
  },
  {
    id: 3,
    author: 'Anonymous',
    isAnonymous: true,
    avatar: '/api/placeholder/40/40',
    content: 'This is a case of a video where the person in the video is seen body shaming women while also...',
    image: '/api/placeholder/300/200',
    url: 'https://www.safeher.com/body-shaming-case-url-here',
    timestamp: '6 hrs ago',
  },
  {
    id: 4,
    author: 'David Mburu',
    isAnonymous: false,
    avatar: '/api/placeholder/40/40',
    content: 'This is a case of a video where the person in the video is seen body shaming women while also...',
    image: '/api/placeholder/300/200',
    url: 'https://www.safeher.com/body-shaming-case-url-here',
    timestamp: '8 hrs ago',
  },
  {
    id: 5,
    author: 'Olivia Orero',
    isAnonymous: false,
    avatar: '/api/placeholder/40/40',
    content: 'This is a case of a video where the person in the video is seen body shaming women while also...',
    image: '/api/placeholder/300/200',
    url: 'https://www.safeher.com/body-shaming-case-url-here',
    timestamp: '12 hrs ago',
  },
  {
    id: 6,
    author: 'Anonymous',
    isAnonymous: true,
    avatar: '/api/placeholder/40/40',
    content: 'This is a case of a video where the person in the video is seen body shaming women while also...',
    image: '/api/placeholder/300/200',
    url: 'https://www.safeher.com/body-shaming-case-url-here',
    timestamp: '1 day ago',
  },
  {
    id: 7,
    author: 'Anonymous',
    isAnonymous: true,
    avatar: '/api/placeholder/40/40',
    content: 'This is a case of a video where the person in the video is seen body shaming women while also...',
    image: '/api/placeholder/300/200',
    url: 'https://www.safeher.com/body-shaming-case-url-here',
    timestamp: '1 day ago',
  },
  {
    id: 8,
    author: 'Safeher',
    isAnonymous: false,
    avatar: '/api/placeholder/40/40',
    content: 'This is a case of a video where the person in the video is seen body shaming women while also...',
    image: '/api/placeholder/300/200',
    url: 'https://www.safeher.com/body-shaming-case-url-here',
    timestamp: '2 days ago',
  },
];

export function ReportsGrid() {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = 10;

  return (
    <div className="space-y-6">
      {/* Reports Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {mockReports.map((report) => (
          <ReportCard key={report.id} report={report} />
        ))}
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className={`px-4 py-2 text-sm font-medium text-text-light border border-border-light rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          Previous
        </button>
        
        <div className="flex items-center space-x-2">
          {[1, 2, 3, '...', 8, 9, 10].map((page, index) => (
            <button
              key={index}
              onClick={() => typeof page === 'number' && setCurrentPage(page)}
              className={`px-3 py-2 text-sm font-medium rounded-lg ${
                page === currentPage
                  ? 'bg-primary text-white'
                  : 'text-text-light hover:bg-gray-50'
              } ${typeof page !== 'number' ? 'cursor-default' : ''}`}
              disabled={typeof page !== 'number'}
            >
              {page}
            </button>
          ))}
        </div>

        <button
          onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
          className={`px-4 py-2 text-sm font-medium text-text-light border border-border-light rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          Next
        </button>
      </div>
    </div>
  );
}

function ReportCard({ report }: { report: typeof mockReports[0] }) {
  return (
    <div className={`${createCardVariant('bordered')} p-4 space-y-4`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <img
            src={report.avatar}
            alt={report.author}
            className="w-8 h-8 rounded-full"
          />
          <div>
            <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
              {report.author}
            </p>
            <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
              {report.timestamp}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button className="p-1 hover:bg-gray-100 rounded">
            <EllipsisHorizontalIcon className="w-4 h-4 text-text-light" />
          </button>
          <button className="p-1 hover:bg-gray-100 rounded">
            <XMarkIcon className="w-4 h-4 text-text-light" />
          </button>
        </div>
      </div>

      {/* Content */}
      <p className={`${getTypography('paragraph-r-12')} text-text-dark`}>
        {report.content}
      </p>

      {/* Image */}
      <div className="relative">
        <img
          src={report.image}
          alt="Report content"
          className="w-full h-32 object-cover rounded-lg"
        />
        <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
          {report.url}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between pt-2 border-t border-border-light">
        <button className="flex items-center space-x-2 text-text-light hover:text-text-dark">
          <ChatBubbleLeftIcon className="w-4 h-4" />
          <span className={`${getTypography('paragraph-r-10')}`}>Comment</span>
        </button>
        <button className="flex items-center space-x-2 text-text-light hover:text-text-dark">
          <PaperAirplaneIcon className="w-4 h-4" />
          <span className={`${getTypography('paragraph-r-10')}`}>Send Message</span>
        </button>
      </div>
    </div>
  );
}
