import { ReactNode } from 'react';
import Navbar from '../navigation/Navbar';
import Footer from './Footer';

interface WebsiteLayoutProps {
  children: ReactNode;
}

export default function WebsiteLayout({ children }: WebsiteLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar variant="website" />
      <main className="flex-1">
        {children}
      </main>
      <Footer />
    </div>
  );
}
