'use client';

import { useState } from 'react';
import { XMarkIcon, PhotoIcon, LinkIcon } from '@heroicons/react/24/outline';
import { getTypography, createButtonVariant } from '@/lib/design-tokens';

interface ReportIncidentModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ReportIncidentModal({ isOpen, onClose }: ReportIncidentModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    url: '',
    platform: '',
    category: '',
    anonymous: false,
  });

  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Report submitted:', formData, selectedFiles);
    onClose();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setSelectedFiles(Array.from(e.target.files));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border-light">
          <h2 className={`${getTypography('title-b-20')} text-text-primary`}>
            Report Incident
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-text-light" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Incident Title *
            </label>
            <input
              type="text"
              required
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Brief description of the incident"
            />
          </div>

          {/* Description */}
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Detailed Description *
            </label>
            <textarea
              required
              rows={4}
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Provide detailed information about the incident..."
            />
          </div>

          {/* URL */}
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              URL/Link to Content
            </label>
            <div className="relative">
              <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-text-light" />
              <input
                type="url"
                value={formData.url}
                onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                className="w-full pl-10 pr-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="https://example.com/harmful-content"
              />
            </div>
          </div>

          {/* Platform and Category */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
                Platform
              </label>
              <select
                value={formData.platform}
                onChange={(e) => setFormData({ ...formData, platform: e.target.value })}
                className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">Select platform</option>
                <option value="facebook">Facebook</option>
                <option value="instagram">Instagram</option>
                <option value="twitter">Twitter</option>
                <option value="tiktok">TikTok</option>
                <option value="youtube">YouTube</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
                Category
              </label>
              <select
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">Select category</option>
                <option value="harassment">Harassment</option>
                <option value="body-shaming">Body Shaming</option>
                <option value="cyberbullying">Cyberbullying</option>
                <option value="hate-speech">Hate Speech</option>
                <option value="doxxing">Doxxing</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>

          {/* File Upload */}
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Evidence (Screenshots, Videos, etc.)
            </label>
            <div className="border-2 border-dashed border-border-light rounded-lg p-6 text-center">
              <PhotoIcon className="mx-auto h-12 w-12 text-text-light" />
              <div className="mt-4">
                <label htmlFor="file-upload" className="cursor-pointer">
                  <span className={`${getTypography('paragraph-m-12')} text-primary hover:text-primary-shade`}>
                    Upload files
                  </span>
                  <input
                    id="file-upload"
                    name="file-upload"
                    type="file"
                    multiple
                    accept="image/*,video/*"
                    className="sr-only"
                    onChange={handleFileChange}
                  />
                </label>
                <p className={`${getTypography('paragraph-r-10')} text-text-light mt-1`}>
                  PNG, JPG, MP4 up to 10MB each
                </p>
              </div>
              {selectedFiles.length > 0 && (
                <div className="mt-4">
                  <p className={`${getTypography('paragraph-m-10')} text-text-primary`}>
                    {selectedFiles.length} file(s) selected
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Anonymous Option */}
          <div className="flex items-center">
            <input
              id="anonymous"
              type="checkbox"
              checked={formData.anonymous}
              onChange={(e) => setFormData({ ...formData, anonymous: e.target.checked })}
              className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
            />
            <label htmlFor="anonymous" className={`ml-2 ${getTypography('paragraph-r-12')} text-text-dark`}>
              Submit this report anonymously
            </label>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-4 pt-4 border-t border-border-light">
            <button
              type="button"
              onClick={onClose}
              className={`px-6 py-2 ${getTypography('paragraph-m-12')} text-text-light border border-border-light rounded-lg hover:bg-gray-50 transition-colors`}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`${createButtonVariant('primary')} ${getTypography('paragraph-m-12')}`}
            >
              Submit Report
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
