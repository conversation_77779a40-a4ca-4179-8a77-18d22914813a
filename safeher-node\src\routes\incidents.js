const express = require('express');
const {
  createIncident,
  getIncidents,
  getIncidentById,
  updateIncidentStatus,
  assignGuardian
} = require('../controllers/incidentController');
const { authenticate, optionalAuth, requireGuardianOrAdmin, requireAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * /api/incidents:
 *   post:
 *     summary: Create a new incident report
 *     tags: [Incidents]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *               - platform
 *               - category
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 5
 *                 maxLength: 500
 *               description:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 5000
 *               platform:
 *                 type: string
 *                 enum: [facebook, instagram, twitter, tiktok, whatsapp, telegram, other]
 *               category:
 *                 type: string
 *                 enum: [body-shaming, harassment, cyberbullying, stalking, threats, doxxing, other]
 *               severity:
 *                 type: string
 *                 enum: [low, medium, high, critical]
 *                 default: medium
 *               url:
 *                 type: string
 *                 format: uri
 *               evidenceUrls:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uri
 *               isAnonymous:
 *                 type: boolean
 *                 default: false
 *               reporterContact:
 *                 type: string
 *                 format: email
 *               location:
 *                 type: string
 *     responses:
 *       201:
 *         description: Incident created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.post('/', optionalAuth, createIncident);

/**
 * @swagger
 * /api/incidents:
 *   get:
 *     summary: Get incidents list
 *     tags: [Incidents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [open, in_review, resolved, closed]
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *       - in: query
 *         name: platform
 *         schema:
 *           type: string
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Incidents retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/', authenticate, getIncidents);

/**
 * @swagger
 * /api/incidents/{id}:
 *   get:
 *     summary: Get incident by ID
 *     tags: [Incidents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Incident retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Incident not found
 */
router.get('/:id', authenticate, getIncidentById);

/**
 * @swagger
 * /api/incidents/{id}/status:
 *   put:
 *     summary: Update incident status
 *     tags: [Incidents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [open, in_review, resolved, closed]
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Status updated successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Incident not found
 */
router.put('/:id/status', authenticate, requireGuardianOrAdmin, updateIncidentStatus);

/**
 * @swagger
 * /api/incidents/{id}/assign:
 *   put:
 *     summary: Assign guardian to incident
 *     tags: [Incidents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - guardianId
 *             properties:
 *               guardianId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Guardian assigned successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Incident or guardian not found
 */
router.put('/:id/assign', authenticate, requireAdmin, assignGuardian);

module.exports = router;
