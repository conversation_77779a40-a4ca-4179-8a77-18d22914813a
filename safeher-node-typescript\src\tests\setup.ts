import dotenv from 'dotenv';
import { sequelize } from '../config/database';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';

// Global test setup
beforeAll(async () => {
  // Ensure we're using test database
  if (!process.env.DB_NAME?.includes('test')) {
    throw new Error('Test database name must contain "test"');
  }

  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Test database connection established');

    // Sync database models for testing
    await sequelize.sync({ force: true });
    console.log('✅ Test database models synchronized');
  } catch (error) {
    console.error('❌ Test database setup failed:', error);
    throw error;
  }
});

// Global test teardown
afterAll(async () => {
  try {
    // Close database connection
    await sequelize.close();
    console.log('✅ Test database connection closed');
  } catch (error) {
    console.error('❌ Test database teardown failed:', error);
  }
});

// Clean up between tests
afterEach(async () => {
  // Clear all tables except roles (needed for foreign key constraints)
  const models = Object.keys(sequelize.models);
  
  for (const modelName of models) {
    if (modelName !== 'Role') {
      await sequelize.models[modelName].destroy({ 
        where: {},
        truncate: true,
        cascade: true,
        restartIdentity: true
      });
    }
  }
});

// Increase timeout for database operations
jest.setTimeout(30000);
