const { Incident, User, Report, Role } = require('../models');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

const createIncident = asyncHandler(async (req, res) => {
  const {
    title,
    description,
    platform,
    category,
    severity = 'medium',
    url,
    evidenceUrls = [],
    isAnonymous = false,
    reporterContact,
    location
  } = req.body;

  // Validate anonymous report requirements
  if (isAnonymous && !reporterContact) {
    throw new CustomError('Anonymous reports must include reporter contact information', 400);
  }

  if (!isAnonymous && !req.user) {
    throw new CustomError('Authentication required for non-anonymous reports', 401);
  }

  const incidentData = {
    title,
    description,
    platform,
    category,
    severity,
    url,
    evidence_urls: evidenceUrls,
    is_anonymous: isAnonymous,
    location,
    status: 'open'
  };

  if (isAnonymous) {
    incidentData.reporter_contact = reporterContact;
    incidentData.reporter_id = null;
  } else {
    incidentData.reporter_id = req.user.id;
    incidentData.reporter_contact = null;
  }

  const incident = await Incident.create(incidentData);

  const response = {
    success: true,
    message: 'Incident reported successfully',
    data: {
      incident: {
        id: incident.id,
        title: incident.title,
        description: incident.description,
        platform: incident.platform,
        category: incident.category,
        status: incident.status,
        severity: incident.severity,
        isAnonymous: incident.is_anonymous,
        createdAt: incident.created_at
      }
    }
  };

  res.status(201).json(response);
});

const getIncidents = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    status,
    category,
    platform,
    severity,
    search
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const whereClause = {};

  // Apply filters
  if (status) whereClause.status = status;
  if (category) whereClause.category = category;
  if (platform) whereClause.platform = platform;
  if (severity) whereClause.severity = severity;

  // Search functionality
  if (search) {
    whereClause[Op.or] = [
      { title: { [Op.like]: `%${search}%` } },
      { description: { [Op.like]: `%${search}%` } }
    ];
  }

  // Role-based access control
  if (req.user?.role === 'user') {
    // Users can only see their own incidents
    whereClause.reporter_id = req.user.id;
  } else if (req.user?.role === 'guardian') {
    // Guardians can see incidents assigned to them or unassigned ones
    whereClause[Op.or] = [
      { guardian_id: req.user.id },
      { guardian_id: null }
    ];
  }
  // Admins can see all incidents (no additional filter)

  const { rows: incidents, count } = await Incident.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'reporter',
        attributes: ['id', 'name', 'email'],
        required: false
      },
      {
        model: User,
        as: 'guardian',
        attributes: ['id', 'name', 'email'],
        required: false
      }
    ],
    limit: parseInt(limit),
    offset,
    order: [['created_at', 'DESC']]
  });

  const response = {
    success: true,
    data: {
      incidents: incidents.map(incident => ({
        id: incident.id,
        title: incident.title,
        description: incident.description,
        platform: incident.platform,
        category: incident.category,
        status: incident.status,
        severity: incident.severity,
        url: incident.url,
        isAnonymous: incident.is_anonymous,
        location: incident.location,
        reporter: incident.reporter ? {
          id: incident.reporter.id,
          name: incident.reporter.name,
          email: incident.is_anonymous ? null : incident.reporter.email
        } : null,
        guardian: incident.guardian ? {
          id: incident.guardian.id,
          name: incident.guardian.name,
          email: incident.guardian.email
        } : null,
        createdAt: incident.created_at,
        updatedAt: incident.updated_at
      })),
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / parseInt(limit))
      }
    }
  };

  res.status(200).json(response);
});

const getIncidentById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const incident = await Incident.findByPk(id, {
    include: [
      {
        model: User,
        as: 'reporter',
        attributes: ['id', 'name', 'email'],
        required: false
      },
      {
        model: User,
        as: 'guardian',
        attributes: ['id', 'name', 'email'],
        required: false
      },
      {
        model: Report,
        as: 'reports',
        include: [
          {
            model: User,
            as: 'reporter',
            attributes: ['id', 'name'],
            required: false
          },
          {
            model: User,
            as: 'guardian',
            attributes: ['id', 'name'],
            required: false
          }
        ],
        order: [['created_at', 'ASC']]
      }
    ]
  });

  if (!incident) {
    throw new CustomError('Incident not found', 404);
  }

  // Check access permissions
  const canAccess = 
    req.user?.role === 'admin' ||
    (req.user?.role === 'guardian' && (incident.guardian_id === req.user.id || !incident.guardian_id)) ||
    (req.user?.role === 'user' && incident.reporter_id === req.user.id);

  if (!canAccess) {
    throw new CustomError('Access denied', 403);
  }

  const response = {
    success: true,
    data: {
      incident: {
        id: incident.id,
        title: incident.title,
        description: incident.description,
        platform: incident.platform,
        category: incident.category,
        status: incident.status,
        severity: incident.severity,
        url: incident.url,
        evidenceUrls: incident.evidence_urls,
        isAnonymous: incident.is_anonymous,
        reporterContact: incident.is_anonymous ? incident.reporter_contact : null,
        location: incident.location,
        reporter: incident.reporter && !incident.is_anonymous ? {
          id: incident.reporter.id,
          name: incident.reporter.name,
          email: incident.reporter.email
        } : null,
        guardian: incident.guardian ? {
          id: incident.guardian.id,
          name: incident.guardian.name,
          email: incident.guardian.email
        } : null,
        reports: incident.reports?.map(report => ({
          id: report.id,
          content: report.content,
          type: report.type,
          isInternal: report.is_internal,
          attachments: report.attachments,
          reporter: report.reporter ? {
            id: report.reporter.id,
            name: report.reporter.name
          } : null,
          guardian: report.guardian ? {
            id: report.guardian.id,
            name: report.guardian.name
          } : null,
          createdAt: report.created_at
        })) || [],
        createdAt: incident.created_at,
        updatedAt: incident.updated_at
      }
    }
  };

  res.status(200).json(response);
});

const updateIncidentStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status, notes } = req.body;

  const incident = await Incident.findByPk(id);

  if (!incident) {
    throw new CustomError('Incident not found', 404);
  }

  // Check permissions
  const canUpdate = 
    req.user?.role === 'admin' ||
    (req.user?.role === 'guardian' && incident.guardian_id === req.user.id);

  if (!canUpdate) {
    throw new CustomError('Only assigned guardians or admins can update incident status', 403);
  }

  // Update incident status
  await incident.update({ status });

  // Create a report for the status update
  if (notes) {
    await Report.create({
      incident_id: incident.id,
      content: notes,
      type: 'update',
      is_internal: false,
      guardian_id: req.user.id
    });
  }

  const response = {
    success: true,
    message: 'Incident status updated successfully',
    data: {
      incident: {
        id: incident.id,
        status: incident.status,
        updatedAt: incident.updated_at
      }
    }
  };

  res.status(200).json(response);
});

const assignGuardian = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { guardianId } = req.body;

  // Only admins can assign guardians
  if (req.user?.role !== 'admin') {
    throw new CustomError('Only admins can assign guardians to incidents', 403);
  }

  const incident = await Incident.findByPk(id);
  if (!incident) {
    throw new CustomError('Incident not found', 404);
  }

  // Verify guardian exists and is approved
  const guardian = await User.findByPk(guardianId, {
    include: [
      {
        model: Role,
        as: 'role',
        where: { name: 'guardian' }
      }
    ]
  });

  if (!guardian) {
    throw new CustomError('Guardian not found or not approved', 404);
  }

  // Assign guardian
  await incident.update({ guardian_id: guardianId });

  const response = {
    success: true,
    message: 'Guardian assigned successfully',
    data: {
      incident: {
        id: incident.id,
        guardianId: incident.guardian_id,
        updatedAt: incident.updated_at
      }
    }
  };

  res.status(200).json(response);
});

module.exports = {
  createIncident,
  getIncidents,
  getIncidentById,
  updateIncidentStatus,
  assignGuardian
};
