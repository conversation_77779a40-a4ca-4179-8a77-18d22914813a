'use strict';

const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcryptjs');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Only run in development environment
    if (process.env.NODE_ENV !== 'development') {
      console.log('Skipping sample data seeder in non-development environment');
      return;
    }

    // Get role IDs
    const [userRole] = await queryInterface.sequelize.query(
      "SELECT id FROM roles WHERE name = 'user'",
      { type: Sequelize.QueryTypes.SELECT }
    );

    const [guardianRole] = await queryInterface.sequelize.query(
      "SELECT id FROM roles WHERE name = 'guardian'",
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (!userRole || !guardianRole) {
      throw new Error('Required roles not found. Please run roles seeder first.');
    }

    // Create sample users
    const hashedPassword = await bcrypt.hash('password123', 12);
    
    const sampleUsers = [
      {
        id: uuidv4(),
        name: '<PERSON>',
        email: '<EMAIL>',
        password_hash: hashedPassword,
        role_id: userRole.id,
        is_active: true,
        email_verified: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'Dr. Sarah Wilson',
        email: '<EMAIL>',
        password_hash: hashedPassword,
        role_id: guardianRole.id,
        is_active: true,
        email_verified: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'Emily Johnson',
        email: '<EMAIL>',
        password_hash: hashedPassword,
        role_id: userRole.id,
        is_active: true,
        email_verified: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('users', sampleUsers, {});

    // Get the guardian user ID
    const [guardianUser] = await queryInterface.sequelize.query(
      "SELECT id FROM users WHERE email = '<EMAIL>'",
      { type: Sequelize.QueryTypes.SELECT }
    );

    // Create guardian profile
    const guardianProfile = {
      id: uuidv4(),
      user_id: guardianUser.id,
      designation: 'Cybersecurity Specialist',
      organization: 'Women\'s Safety Foundation',
      bio: 'Experienced cybersecurity professional specializing in online harassment prevention and digital safety education.',
      expertise_areas: ['cybersecurity', 'digital_privacy', 'online_harassment', 'legal_advice'],
      status: 'approved',
      resources_count: 0,
      reports_count: 0,
      approved_by: null, // Would be admin in real scenario
      approved_at: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    };

    await queryInterface.bulkInsert('guardians', [guardianProfile], {});

    // Create sample incidents
    const [regularUser] = await queryInterface.sequelize.query(
      "SELECT id FROM users WHERE email = '<EMAIL>'",
      { type: Sequelize.QueryTypes.SELECT }
    );

    const sampleIncidents = [
      {
        id: uuidv4(),
        title: 'Harassment on Social Media Platform',
        description: 'Receiving threatening messages and inappropriate comments on my posts.',
        platform: 'instagram',
        category: 'harassment',
        status: 'open',
        severity: 'medium',
        url: 'https://instagram.com/example-post',
        evidence_urls: [],
        is_anonymous: false,
        reporter_id: regularUser.id,
        guardian_id: guardianUser.id,
        location: 'Online',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        title: 'Anonymous Report - Cyberbullying',
        description: 'Group of users targeting and bullying women in online gaming community.',
        platform: 'other',
        category: 'cyberbullying',
        status: 'in_progress',
        severity: 'high',
        evidence_urls: [],
        is_anonymous: true,
        reporter_contact: '<EMAIL>',
        guardian_id: guardianUser.id,
        location: 'Gaming Platform',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('incidents', sampleIncidents, {});

    // Create sample resources
    const sampleResources = [
      {
        id: uuidv4(),
        title: 'Digital Privacy Protection Guide',
        description: 'Comprehensive guide on protecting your privacy online and securing your digital accounts.',
        content: 'This guide covers essential steps for maintaining digital privacy including strong password practices, two-factor authentication, privacy settings on social media platforms, and recognizing phishing attempts...',
        category: 'safety',
        tags: ['privacy', 'security', 'social_media', 'passwords'],
        attachment_urls: [],
        is_published: true,
        views_count: 45,
        likes_count: 12,
        author_id: guardianUser.id,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        title: 'Legal Rights in Cyberbullying Cases',
        description: 'Understanding your legal rights and options when facing online harassment and cyberbullying.',
        content: 'This resource outlines the legal framework surrounding cyberbullying, steps to document evidence, reporting procedures, and available legal remedies...',
        category: 'legal',
        tags: ['legal_rights', 'cyberbullying', 'harassment', 'documentation'],
        attachment_urls: [],
        is_published: true,
        views_count: 32,
        likes_count: 8,
        author_id: guardianUser.id,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('resources', sampleResources, {});

    console.log('✅ Sample data created successfully!');
    console.log('👥 Sample users created with password: password123');
    console.log('📧 Guardian: <EMAIL>');
    console.log('📧 User: <EMAIL>');
    console.log('📧 User: <EMAIL>');
  },

  async down(queryInterface, Sequelize) {
    // Clean up in reverse order due to foreign key constraints
    await queryInterface.bulkDelete('resources', null, {});
    await queryInterface.bulkDelete('incidents', null, {});
    await queryInterface.bulkDelete('guardians', null, {});
    await queryInterface.bulkDelete('users', {
      email: {
        [Sequelize.Op.in]: [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>'
        ]
      }
    }, {});
  }
};
