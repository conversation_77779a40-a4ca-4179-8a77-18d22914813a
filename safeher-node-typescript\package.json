{"name": "safeher-node", "version": "1.0.0", "description": "SafeHer Backend API - Node.js Express server for women's online safety platform", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "npx sequelize-cli db:migrate", "db:seed": "npx sequelize-cli db:seed:all", "db:reset": "npx sequelize-cli db:drop && npx sequelize-cli db:create && npm run db:migrate && npm run db:seed", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["safeher", "api", "women-safety", "cyberbullying", "express", "nodejs"], "author": "SafeHer Team", "license": "MIT", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "sequelize": "^6.37.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "zod": "^3.25.71"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.10", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "jest": "^30.0.4", "nodemon": "^3.1.10", "prettier": "^3.6.2", "sequelize-cli": "^6.6.3", "supertest": "^7.1.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}