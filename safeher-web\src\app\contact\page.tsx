import WebsiteLayout from '@/components/global/layout/WebsiteLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

export default function ContactPage() {
  return (
    <WebsiteLayout>
      <div className="bg-white py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Contact Us
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
              Have questions, need support, or want to get involved? We're here to help. 
              Reach out to us and we'll get back to you as soon as possible.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-gray-50 rounded-2xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Send us a message</h2>
              <form className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <Input
                    label="First name"
                    type="text"
                    required
                    placeholder="Your first name"
                  />
                  <Input
                    label="Last name"
                    type="text"
                    required
                    placeholder="Your last name"
                  />
                </div>

                <Input
                  label="Email address"
                  type="email"
                  required
                  placeholder="<EMAIL>"
                />

                <Input
                  label="Subject"
                  type="text"
                  required
                  placeholder="What is this about?"
                />

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Message
                  </label>
                  <textarea
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Tell us more about your inquiry..."
                    required
                  />
                </div>

                <Button type="submit" className="w-full">
                  Send Message
                </Button>
              </form>
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Get in touch</h2>
                <p className="text-gray-600 mb-8">
                  We're committed to supporting women's safety online. Whether you need immediate 
                  assistance or want to learn more about our mission, don't hesitate to reach out.
                </p>
              </div>

              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-blue-100 rounded-lg p-3">
                    <span className="text-2xl">📧</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Email</h3>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-green-100 rounded-lg p-3">
                    <span className="text-2xl">📱</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Emergency Hotline</h3>
                    <p className="text-gray-600">+254 700 000 000</p>
                    <p className="text-sm text-gray-500">Available 24/7 for urgent cases</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-purple-100 rounded-lg p-3">
                    <span className="text-2xl">📍</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Office</h3>
                    <p className="text-gray-600">
                      SafeOnline Women Kenya<br />
                      Nairobi, Kenya
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-pink-100 rounded-lg p-3">
                    <span className="text-2xl">🌐</span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Social Media</h3>
                    <div className="flex space-x-4 mt-2">
                      <a href="#" className="text-blue-600 hover:text-blue-800">Twitter</a>
                      <a href="#" className="text-blue-600 hover:text-blue-800">Facebook</a>
                      <a href="#" className="text-blue-600 hover:text-blue-800">Instagram</a>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h3 className="font-semibold text-red-800 mb-2">Emergency Support</h3>
                <p className="text-red-700 text-sm">
                  If you're experiencing immediate danger or urgent online harassment, 
                  please contact local authorities or our emergency hotline immediately.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </WebsiteLayout>
  );
}
