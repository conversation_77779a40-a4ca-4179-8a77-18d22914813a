import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import Card from '../../components/ui/Card';
import { colors, getTypography, spacing, borderRadius, createInputStyle } from '../../lib/design-tokens';

type CategoryType = 'All' | 'Safety Tips' | 'Legal Help' | 'Support Groups' | 'Emergency';

export default function ResourcesScreen() {
  const [activeCategory, setActiveCategory] = useState<CategoryType>('All');
  const [searchQuery, setSearchQuery] = useState('');

  const categories: CategoryType[] = ['All', 'Safety Tips', 'Legal Help', 'Support Groups', 'Emergency'];

  const resources = [
    {
      title: 'Online Safety Guide',
      description: 'Comprehensive guide to staying safe online',
      category: 'Safety Tips',
      icon: 'shield-checkmark',
      color: colors.primary,
    },
    {
      title: 'Legal Support Hotline',
      description: '24/7 legal assistance for online harassment',
      category: 'Legal Help',
      icon: 'call',
      color: colors.secondary,
    },
    {
      title: 'Women Support Network',
      description: 'Connect with other women for support',
      category: 'Support Groups',
      icon: 'people',
      color: colors.primaryShade,
    },
    {
      title: 'Emergency Contacts',
      description: 'Quick access to emergency services',
      category: 'Emergency',
      icon: 'alert-circle',
      color: colors.error,
    },
  ];

  const filteredResources = activeCategory === 'All' 
    ? resources 
    : resources.filter(resource => resource.category === activeCategory);

  const renderResourceCard = (resource: any, index: number) => (
    <Card key={index} variant="bordered" style={styles.resourceCard} onPress={() => {}}>
      <View style={styles.resourceHeader}>
        <View style={[styles.resourceIcon, { backgroundColor: resource.color }]}>
          <Ionicons name={resource.icon} size={24} color={colors.primaryWhite} />
        </View>
        <View style={styles.resourceContent}>
          <Text style={styles.resourceTitle}>{resource.title}</Text>
          <Text style={styles.resourceDescription}>{resource.description}</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Resources</Text>
        <TouchableOpacity style={styles.bookmarkButton}>
          <Ionicons name="bookmark-outline" size={24} color={colors.primaryWhite} />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color={colors.textLight} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search resources..."
            placeholderTextColor={colors.textLighter}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      {/* Categories */}
      <View style={styles.categoryContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {categories.map((category) => (
            <TouchableOpacity
              key={category}
              style={[
                styles.categoryChip,
                activeCategory === category && styles.activeCategoryChip,
              ]}
              onPress={() => setActiveCategory(category)}
            >
              <Text
                style={[
                  styles.categoryText,
                  activeCategory === category && styles.activeCategoryText,
                ]}
              >
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Featured Section */}
      <View style={styles.featuredSection}>
        <Text style={styles.sectionTitle}>Featured</Text>
        <Card variant="elevated" style={styles.featuredCard}>
          <View style={styles.featuredContent}>
            <View style={styles.featuredIcon}>
              <Ionicons name="star" size={24} color={colors.warning} />
            </View>
            <View style={styles.featuredText}>
              <Text style={styles.featuredTitle}>Safety Assessment</Text>
              <Text style={styles.featuredDescription}>
                Take our quick assessment to get personalized safety recommendations
              </Text>
            </View>
          </View>
        </Card>
      </View>

      {/* Resources List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.resourcesList}>
          <Text style={styles.sectionTitle}>
            {activeCategory === 'All' ? 'All Resources' : activeCategory}
          </Text>
          
          {filteredResources.map((resource, index) => renderResourceCard(resource, index))}
          
          {filteredResources.length === 0 && (
            <View style={styles.emptyState}>
              <Ionicons name="folder-outline" size={64} color={colors.textLighter} />
              <Text style={styles.emptyTitle}>No Resources Found</Text>
              <Text style={styles.emptyDescription}>
                Try adjusting your search or category filter
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    paddingTop: spacing.lg,
  },
  headerTitle: {
    ...getTypography('title-b-20'),
    color: colors.primaryWhite,
  },
  bookmarkButton: {
    padding: spacing.sm,
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.primary,
  },
  searchBar: {
    ...createInputStyle(),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primaryWhite,
    borderColor: 'transparent',
  },
  searchIcon: {
    marginRight: spacing.sm,
  },
  searchInput: {
    flex: 1,
    ...getTypography('paragraph-m-16'),
    color: colors.textPrimary,
    paddingVertical: 0,
  },
  categoryContainer: {
    paddingVertical: spacing.md,
    paddingLeft: spacing.lg,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  categoryChip: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginRight: spacing.sm,
    borderRadius: borderRadius.full,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  activeCategoryChip: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryText: {
    ...getTypography('paragraph-m-12'),
    color: colors.textPrimary,
  },
  activeCategoryText: {
    color: colors.primaryWhite,
  },
  featuredSection: {
    padding: spacing.lg,
    paddingBottom: 0,
  },
  sectionTitle: {
    ...getTypography('title-b-16'),
    color: colors.textPrimary,
    marginBottom: spacing.md,
  },
  featuredCard: {
    backgroundColor: colors.primaryLightShade + '20',
    borderColor: colors.primaryLightShade,
    borderWidth: 1,
  },
  featuredContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  featuredIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primaryWhite,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  featuredText: {
    flex: 1,
  },
  featuredTitle: {
    ...getTypography('lead-sm-16'),
    color: colors.textPrimary,
    marginBottom: spacing.xs,
  },
  featuredDescription: {
    ...getTypography('paragraph-r-12'),
    color: colors.textLight,
  },
  content: {
    flex: 1,
  },
  resourcesList: {
    padding: spacing.lg,
  },
  resourceCard: {
    marginBottom: spacing.sm,
  },
  resourceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resourceIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  resourceContent: {
    flex: 1,
  },
  resourceTitle: {
    ...getTypography('lead-sm-16'),
    color: colors.textPrimary,
    marginBottom: spacing.xs,
  },
  resourceDescription: {
    ...getTypography('paragraph-r-12'),
    color: colors.textLight,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    ...getTypography('title-b-16'),
    color: colors.textPrimary,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptyDescription: {
    ...getTypography('paragraph-r-12'),
    color: colors.textLight,
    textAlign: 'center',
  },
});
