'use client';

import Link from 'next/link';
import { useState } from 'react';

interface NavbarProps {
  variant?: 'website' | 'dashboard';
}

export default function Navbar({ variant = 'website' }: NavbarProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  if (variant === 'dashboard') {
    return null; // Dashboard uses sidebar navigation
  }

  return (
    <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="text-2xl font-bold text-pink-600">
              SafeHer
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-8">
              <Link
                href="/landing"
                className="text-gray-700 hover:text-pink-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                For Her
              </Link>
              <Link
                href="/for-guardians"
                className="text-gray-700 hover:text-pink-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                For Guardians
              </Link>
              <Link
                href="/about"
                className="text-gray-700 hover:text-pink-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                About us
              </Link>
              <Link
                href="/contact"
                className="text-gray-700 hover:text-pink-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                Contact us
              </Link>
            </div>
          </div>

          {/* Auth Buttons */}
          <div className="hidden md:block">
            <div className="ml-4 flex items-center space-x-4">
              <Link
                href="/login"
                className="text-gray-700 hover:text-pink-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                Login
              </Link>
              <Link
                href="/register"
                className="bg-pink-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-pink-700 transition-colors"
              >
                Get started its - free →
              </Link>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-pink-600 focus:outline-none focus:text-pink-600"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
              <Link
                href="/landing"
                className="text-gray-700 hover:text-pink-600 block px-3 py-2 text-base font-medium"
              >
                For Her
              </Link>
              <Link
                href="/for-guardians"
                className="text-gray-700 hover:text-pink-600 block px-3 py-2 text-base font-medium"
              >
                For Guardians
              </Link>
              <Link
                href="/about"
                className="text-gray-700 hover:text-pink-600 block px-3 py-2 text-base font-medium"
              >
                About us
              </Link>
              <Link
                href="/contact"
                className="text-gray-700 hover:text-pink-600 block px-3 py-2 text-base font-medium"
              >
                Contact us
              </Link>
              <Link
                href="/login"
                className="text-gray-700 hover:text-pink-600 block px-3 py-2 text-base font-medium"
              >
                Login
              </Link>
              <Link
                href="/register"
                className="bg-pink-600 text-white block px-3 py-2 text-base font-medium rounded-lg mx-3 text-center"
              >
                Get started its - free →
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
