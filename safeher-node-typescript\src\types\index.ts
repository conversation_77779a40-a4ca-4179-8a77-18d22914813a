import { Request } from 'express';

// User Types
export interface UserAttributes {
  id: string;
  name: string;
  email: string;
  password_hash: string;
  role_id: string;
  avatar?: string;
  is_active: boolean;
  email_verified: boolean;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface UserCreationAttributes extends Omit<UserAttributes, 'id' | 'created_at' | 'updated_at'> {}

// Role Types
export interface RoleAttributes {
  id: string;
  name: 'admin' | 'guardian' | 'user';
  description?: string;
  permissions?: string[];
  created_at: Date;
  updated_at: Date;
}

export interface RoleCreationAttributes extends Omit<RoleAttributes, 'id' | 'created_at' | 'updated_at'> {}

// Guardian Types
export interface GuardianAttributes {
  id: string;
  user_id: string;
  designation: string;
  organization?: string;
  bio?: string;
  expertise_areas?: string[];
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  approved_by?: string;
  approved_at?: Date;
  resources_count: number;
  reports_count: number;
  created_at: Date;
  updated_at: Date;
}

export interface GuardianCreationAttributes extends Omit<GuardianAttributes, 'id' | 'created_at' | 'updated_at'> {}

// Incident Types
export interface IncidentAttributes {
  id: string;
  reporter_id?: string; // nullable for anonymous reports
  guardian_id?: string;
  title: string;
  description: string;
  platform: 'facebook' | 'instagram' | 'twitter' | 'tiktok' | 'whatsapp' | 'telegram' | 'other';
  category: 'body-shaming' | 'harassment' | 'cyberbullying' | 'stalking' | 'threats' | 'doxxing' | 'other';
  status: 'open' | 'in_review' | 'resolved' | 'closed';
  severity: 'low' | 'medium' | 'high' | 'critical';
  url?: string;
  evidence_urls?: string[];
  is_anonymous: boolean;
  reporter_contact?: string; // for anonymous reports
  location?: string;
  created_at: Date;
  updated_at: Date;
}

export interface IncidentCreationAttributes extends Omit<IncidentAttributes, 'id' | 'created_at' | 'updated_at'> {}

// Report Types (follow-up reports on incidents)
export interface ReportAttributes {
  id: string;
  incident_id: string;
  reporter_id?: string;
  guardian_id?: string;
  content: string;
  type: 'update' | 'comment' | 'evidence' | 'resolution';
  is_internal: boolean; // internal notes vs public updates
  attachments?: string[];
  created_at: Date;
  updated_at: Date;
}

export interface ReportCreationAttributes extends Omit<ReportAttributes, 'id' | 'created_at' | 'updated_at'> {}

// Resource Types
export interface ResourceAttributes {
  id: string;
  title: string;
  description: string;
  content: string;
  category: 'guide' | 'article' | 'video' | 'tool' | 'legal' | 'support';
  author_id: string;
  tags?: string[];
  featured: boolean;
  published: boolean;
  views_count: number;
  likes_count: number;
  download_url?: string;
  external_url?: string;
  created_at: Date;
  updated_at: Date;
}

export interface ResourceCreationAttributes extends Omit<ResourceAttributes, 'id' | 'created_at' | 'updated_at'> {}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
  statusCode?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Request Types
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: string;
    roleId: string;
  };
}

// Query Types
export interface PaginationQuery {
  page?: string;
  limit?: string;
  sort?: string;
  order?: 'ASC' | 'DESC';
}

export interface SearchQuery extends PaginationQuery {
  search?: string;
  filter?: string;
}

// Dashboard Stats
export interface DashboardStats {
  totalIncidents: number;
  totalGuardians: number;
  totalUsers: number;
  totalReports: number;
  incidentsOverTime: {
    date: string;
    count: number;
    platform?: string;
  }[];
  incidentsByCategory: {
    category: string;
    count: number;
  }[];
  incidentsByStatus: {
    status: string;
    count: number;
  }[];
}

// JWT Payload
export interface JwtPayload {
  id: string;
  email: string;
  role: string;
  roleId: string;
  iat?: number;
  exp?: number;
}
