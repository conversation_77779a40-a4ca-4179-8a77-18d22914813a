const { ZodError } = require('zod');
const { CustomError } = require('./errorHandler');

const validate = (schema) => {
  return async (req, res, next) => {
    try {
      await schema.parseAsync({
        body: req.body,
        query: req.query,
        params: req.params,
      });
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        next(error);
      } else {
        next(new CustomError('Validation error', 400));
      }
    }
  };
};

module.exports = { validate };
