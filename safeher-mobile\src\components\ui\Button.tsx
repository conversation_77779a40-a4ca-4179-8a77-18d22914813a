import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { colors, createButtonStyle, getTypography } from '../../lib/design-tokens';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export default function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
}: ButtonProps) {
  const buttonStyle = createButtonStyle(variant);
  
  const sizeStyles = {
    small: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      minHeight: 36,
    },
    medium: {
      paddingHorizontal: 24,
      paddingVertical: 12,
      minHeight: 48,
    },
    large: {
      paddingHorizontal: 32,
      paddingVertical: 16,
      minHeight: 56,
    },
  };

  const getTextColor = () => {
    if (disabled) return colors.textLighter;
    
    switch (variant) {
      case 'primary':
      case 'secondary':
        return colors.primaryWhite;
      case 'outline':
        return colors.primary;
      case 'ghost':
        return colors.primary;
      default:
        return colors.primaryWhite;
    }
  };

  const getTextStyle = () => {
    const baseTextStyle = getTypography('button-r-20');
    return {
      ...baseTextStyle,
      color: getTextColor(),
      fontSize: size === 'small' ? 14 : size === 'large' ? 18 : 16,
    };
  };

  return (
    <TouchableOpacity
      style={[
        buttonStyle,
        sizeStyles[size],
        disabled && styles.disabled,
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator color={getTextColor()} size="small" />
      ) : (
        <Text style={[getTextStyle(), textStyle]}>{title}</Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  disabled: {
    opacity: 0.5,
  },
});
