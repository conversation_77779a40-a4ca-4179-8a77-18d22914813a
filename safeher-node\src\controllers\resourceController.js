const { Resource, User, Guardian } = require('../models');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');
const { Op } = require('sequelize');

const createResource = asyncHandler(async (req, res) => {
  const {
    title,
    description,
    content,
    category,
    tags = [],
    featured = false,
    published = false,
    downloadUrl,
    externalUrl
  } = req.body;

  // Only guardians and admins can create resources
  if (!['guardian', 'admin'].includes(req.user?.role)) {
    throw new CustomError('Only guardians and admins can create resources', 403);
  }

  const resource = await Resource.create({
    title,
    description,
    content,
    category,
    tags,
    featured: req.user.role === 'admin' ? featured : false, // Only admins can set featured
    published: req.user.role === 'admin' ? published : false, // Resources need admin approval
    download_url: downloadUrl,
    external_url: externalUrl,
    author_id: req.user.id,
    views_count: 0,
    likes_count: 0
  });

  const response = {
    success: true,
    message: 'Resource created successfully',
    data: {
      resource: {
        id: resource.id,
        title: resource.title,
        description: resource.description,
        category: resource.category,
        tags: resource.tags,
        featured: resource.featured,
        published: resource.published,
        authorId: resource.author_id,
        createdAt: resource.created_at
      }
    }
  };

  res.status(201).json(response);
});

const getResources = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    category,
    featured,
    search,
    authorId,
    published = 'true' // Default to only published resources for public access
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const whereClause = {};

  // Apply filters
  if (category) whereClause.category = category;
  if (featured !== undefined) whereClause.featured = featured === 'true';
  if (authorId) whereClause.author_id = authorId;

  // Published filter - admins can see all, others only published
  if (req.user?.role !== 'admin') {
    whereClause.published = true;
  } else if (published !== undefined) {
    whereClause.published = published === 'true';
  }

  // Search functionality
  if (search) {
    whereClause[Op.or] = [
      { title: { [Op.like]: `%${search}%` } },
      { description: { [Op.like]: `%${search}%` } },
      { content: { [Op.like]: `%${search}%` } }
    ];
  }

  const { rows: resources, count } = await Resource.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'name'],
        include: [
          {
            model: Guardian,
            as: 'guardian',
            attributes: ['designation', 'organization'],
            required: false
          }
        ]
      }
    ],
    limit: parseInt(limit),
    offset,
    order: [
      ['featured', 'DESC'],
      ['created_at', 'DESC']
    ]
  });

  const response = {
    success: true,
    data: {
      resources: resources.map(resource => ({
        id: resource.id,
        title: resource.title,
        description: resource.description,
        category: resource.category,
        tags: resource.tags,
        featured: resource.featured,
        published: resource.published,
        viewsCount: resource.views_count,
        likesCount: resource.likes_count,
        downloadUrl: resource.download_url,
        externalUrl: resource.external_url,
        author: {
          id: resource.author.id,
          name: resource.author.name,
          designation: resource.author.guardian?.designation,
          organization: resource.author.guardian?.organization
        },
        createdAt: resource.created_at,
        updatedAt: resource.updated_at
      })),
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / parseInt(limit))
      }
    }
  };

  res.status(200).json(response);
});

const getResourceById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const resource = await Resource.findByPk(id, {
    include: [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'name'],
        include: [
          {
            model: Guardian,
            as: 'guardian',
            attributes: ['designation', 'organization', 'bio'],
            required: false
          }
        ]
      }
    ]
  });

  if (!resource) {
    throw new CustomError('Resource not found', 404);
  }

  // Check if user can access unpublished resources
  if (!resource.published && req.user?.role !== 'admin' && resource.author_id !== req.user?.id) {
    throw new CustomError('Resource not found', 404);
  }

  // Increment view count (only for published resources)
  if (resource.published) {
    await resource.increment('views_count');
  }

  const response = {
    success: true,
    data: {
      resource: {
        id: resource.id,
        title: resource.title,
        description: resource.description,
        content: resource.content,
        category: resource.category,
        tags: resource.tags,
        featured: resource.featured,
        published: resource.published,
        viewsCount: resource.views_count + (resource.published ? 1 : 0),
        likesCount: resource.likes_count,
        downloadUrl: resource.download_url,
        externalUrl: resource.external_url,
        author: {
          id: resource.author.id,
          name: resource.author.name,
          designation: resource.author.guardian?.designation,
          organization: resource.author.guardian?.organization,
          bio: resource.author.guardian?.bio
        },
        createdAt: resource.created_at,
        updatedAt: resource.updated_at
      }
    }
  };

  res.status(200).json(response);
});

const updateResource = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    title,
    description,
    content,
    category,
    tags,
    featured,
    published,
    downloadUrl,
    externalUrl
  } = req.body;

  const resource = await Resource.findByPk(id);

  if (!resource) {
    throw new CustomError('Resource not found', 404);
  }

  // Check permissions
  const canUpdate = 
    req.user?.role === 'admin' ||
    resource.author_id === req.user?.id;

  if (!canUpdate) {
    throw new CustomError('You can only update your own resources', 403);
  }

  const updateData = {
    title,
    description,
    content,
    category,
    tags,
    download_url: downloadUrl,
    external_url: externalUrl
  };

  // Only admins can update featured and published status
  if (req.user.role === 'admin') {
    if (featured !== undefined) updateData.featured = featured;
    if (published !== undefined) updateData.published = published;
  }

  await resource.update(updateData);

  const response = {
    success: true,
    message: 'Resource updated successfully',
    data: {
      resource: {
        id: resource.id,
        title: resource.title,
        description: resource.description,
        category: resource.category,
        featured: resource.featured,
        published: resource.published,
        updatedAt: resource.updated_at
      }
    }
  };

  res.status(200).json(response);
});

const deleteResource = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const resource = await Resource.findByPk(id);

  if (!resource) {
    throw new CustomError('Resource not found', 404);
  }

  // Check permissions
  const canDelete = 
    req.user?.role === 'admin' ||
    resource.author_id === req.user?.id;

  if (!canDelete) {
    throw new CustomError('You can only delete your own resources', 403);
  }

  await resource.destroy();

  const response = {
    success: true,
    message: 'Resource deleted successfully'
  };

  res.status(200).json(response);
});

const likeResource = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const resource = await Resource.findByPk(id);

  if (!resource) {
    throw new CustomError('Resource not found', 404);
  }

  if (!resource.published) {
    throw new CustomError('Cannot like unpublished resource', 400);
  }

  // Increment likes count
  await resource.increment('likes_count');

  const response = {
    success: true,
    message: 'Resource liked successfully',
    data: {
      resource: {
        id: resource.id,
        likesCount: resource.likes_count + 1
      }
    }
  };

  res.status(200).json(response);
});

module.exports = {
  createResource,
  getResources,
  getResourceById,
  updateResource,
  deleteResource,
  likeResource
};
