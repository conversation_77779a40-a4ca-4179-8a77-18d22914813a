import { TextStyle, ViewStyle } from 'react-native';

// Color tokens from Figma design system
export const colors = {
  primary: '#cf1884',
  secondary: '#3364b3',
  primaryWhite: '#ffffff',
  primaryShade: '#f651ad',
  secondaryShade: '#5f8edb',
  primaryLightShade: '#fe83c8',
  secondaryLightShade: '#98b0d6',
  borderDark: '#d0d5dd',
  borderLight: '#eaecf0',
  textPrimary: '#2b1a55',
  textDark: '#48396c',
  textLight: '#706390',
  textLighter: '#c0b9d1',
  
  // Additional utility colors
  background: '#ffffff',
  surface: '#f8f9fa',
  error: '#dc3545',
  success: '#28a745',
  warning: '#ffc107',
  info: '#17a2b8',
} as const;

// Typography tokens from Figma design system
export const typography = {
  // Title styles
  'title-bold-64': {
    fontFamily: 'SourceSansPro-Bold',
    fontSize: 64,
    fontWeight: '700' as const,
    lineHeight: 76.8,
  },
  'title-b-20': {
    fontFamily: 'SourceSansPro-Bold',
    fontSize: 20,
    fontWeight: '700' as const,
    lineHeight: 24,
  },
  'title-b-16': {
    fontFamily: 'SourceSansPro-Bold',
    fontSize: 18,
    fontWeight: '700' as const,
    lineHeight: 21.6,
  },
  'title-b-14': {
    fontFamily: 'SourceSansPro-Bold',
    fontSize: 14,
    fontWeight: '700' as const,
    lineHeight: 16.8,
  },
  
  // Lead styles
  'lead-r-27': {
    fontFamily: 'SourceSansPro-Regular',
    fontSize: 27,
    fontWeight: '400' as const,
    lineHeight: 37.8,
  },
  'lead-b-12': {
    fontFamily: 'SourceSansPro-Bold',
    fontSize: 14,
    fontWeight: '700' as const,
    lineHeight: 19.6,
  },
  'lead-r-14': {
    fontFamily: 'SourceSansPro-Regular',
    fontSize: 14,
    fontWeight: '400' as const,
    lineHeight: 19.6,
  },
  'lead-r-10': {
    fontFamily: 'SourceSansPro-Regular',
    fontSize: 10,
    fontWeight: '400' as const,
    lineHeight: 14,
  },
  'lead-sm-20': {
    fontFamily: 'SourceSansPro-SemiBold',
    fontSize: 20,
    fontWeight: '600' as const,
    lineHeight: 28,
  },
  'lead-sm-16': {
    fontFamily: 'SourceSansPro-SemiBold',
    fontSize: 16,
    fontWeight: '600' as const,
    lineHeight: 22.4,
  },
  'lead-sm-10': {
    fontFamily: 'SourceSansPro-SemiBold',
    fontSize: 10,
    fontWeight: '600' as const,
    lineHeight: 14,
  },
  
  // Paragraph styles
  'paragraph-r-20': {
    fontFamily: 'Nunito-Regular',
    fontSize: 20,
    fontWeight: '400' as const,
    lineHeight: 32,
  },
  'paragraph-m-16': {
    fontFamily: 'Nunito-Medium',
    fontSize: 16,
    fontWeight: '500' as const,
    lineHeight: 25.6,
  },
  'paragraph-m-12': {
    fontFamily: 'Nunito-Medium',
    fontSize: 12,
    fontWeight: '500' as const,
    lineHeight: 14.4,
  },
  'paragraph-r-12': {
    fontFamily: 'Nunito-Regular',
    fontSize: 12,
    fontWeight: '400' as const,
    lineHeight: 14.4,
  },
  'paragraph-m-10': {
    fontFamily: 'Nunito-Medium',
    fontSize: 12,
    fontWeight: '500' as const,
    lineHeight: 19.2,
  },
  'paragraph-r-10': {
    fontFamily: 'Nunito-Regular',
    fontSize: 10,
    fontWeight: '400' as const,
    lineHeight: 12,
  },
  
  // Link styles
  'link-r-20': {
    fontFamily: 'Montserrat-Regular',
    fontSize: 20,
    fontWeight: '400' as const,
    lineHeight: 32,
  },
  'link-r-16': {
    fontFamily: 'Montserrat-Regular',
    fontSize: 16,
    fontWeight: '400' as const,
    lineHeight: 25.6,
  },
  'link-r-12': {
    fontFamily: 'Montserrat-Regular',
    fontSize: 12,
    fontWeight: '400' as const,
    lineHeight: 19.2,
  },
  
  // Button styles
  'button-r-20': {
    fontFamily: 'Montserrat-Bold',
    fontSize: 20,
    fontWeight: '700' as const,
    lineHeight: 32,
  },
} as const;

// Shadow tokens from Figma design system
export const shadows = {
  dropShadow: {
    shadowColor: '#d0d5dd',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 4, // Android
  },
  presentationShadow: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.17,
    shadowRadius: 38,
    elevation: 17, // Android
  },
} as const;

// Spacing system
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
} as const;

// Border radius system
export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 24,
  full: 9999,
} as const;

// Utility functions for creating consistent styles
export const getTypography = (variant: keyof typeof typography): TextStyle => {
  return typography[variant];
};

export const createButtonStyle = (variant: 'primary' | 'secondary' | 'outline' | 'ghost'): ViewStyle => {
  const baseStyle: ViewStyle = {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  };

  switch (variant) {
    case 'primary':
      return {
        ...baseStyle,
        backgroundColor: colors.primary,
        ...shadows.dropShadow,
      };
    case 'secondary':
      return {
        ...baseStyle,
        backgroundColor: colors.secondary,
        ...shadows.dropShadow,
      };
    case 'outline':
      return {
        ...baseStyle,
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: colors.primary,
      };
    case 'ghost':
      return {
        ...baseStyle,
        backgroundColor: 'transparent',
      };
    default:
      return baseStyle;
  }
};

export const createCardStyle = (variant: 'default' | 'elevated' | 'bordered'): ViewStyle => {
  const baseStyle: ViewStyle = {
    backgroundColor: colors.background,
    borderRadius: borderRadius.lg,
    padding: spacing.md,
  };

  switch (variant) {
    case 'elevated':
      return {
        ...baseStyle,
        ...shadows.dropShadow,
      };
    case 'bordered':
      return {
        ...baseStyle,
        borderWidth: 1,
        borderColor: colors.borderLight,
      };
    default:
      return baseStyle;
  }
};

export const createInputStyle = (): ViewStyle => ({
  borderWidth: 1,
  borderColor: colors.borderDark,
  borderRadius: borderRadius.lg,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.md,
  backgroundColor: colors.background,
  minHeight: 48,
});

// Gradient definitions (for use with react-native-linear-gradient)
export const gradients = {
  linearOne: {
    colors: ['#cf188400', '#cf18844a'],
    start: { x: 0, y: 0 },
    end: { x: 1, y: 1 },
  },
  linearMix: {
    colors: ['#5b8cdb3a', '#fe83c81a'],
    start: { x: 0, y: 0 },
    end: { x: 0, y: 1 },
  },
} as const;
