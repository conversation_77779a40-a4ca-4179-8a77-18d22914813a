@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* SafeHer Design Tokens - Colors */
  --primary-color: #cf1884;
  --secondary-color: #3364b3;
  --primary-white: #ffffff;
  --primary-shade: #f651ad;
  --secondary-shade: #5f8edb;
  --primary-light-shade: #fe83c8;
  --secondary-light-shade: #98b0d6;
  --border-dark: #d0d5dd;
  --border-light: #eaecf0;
  --primary-text: #2b1a55;
  --primary-dark-text: #48396c;
  --primary-light-text: #706390;
  --primary-lighter-text: #c0b9d1;

  /* SafeHer Design Tokens - Shadows */
  --shadow-sm: 0 4px 10px rgba(208, 213, 221, 0.25);
  --shadow-presentation: 0 0 38px rgba(0, 0, 0, 0.17), 0 0 17px rgba(0, 0, 0, 0.17);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* SafeHer Colors for Tailwind */
  --color-primary: var(--primary-color);
  --color-secondary: var(--secondary-color);
  --color-primary-shade: var(--primary-shade);
  --color-secondary-shade: var(--secondary-shade);
  --color-primary-light: var(--primary-light-shade);
  --color-secondary-light: var(--secondary-light-shade);
  --color-border-dark: var(--border-dark);
  --color-border-light: var(--border-light);
  --color-text-primary: var(--primary-text);
  --color-text-dark: var(--primary-dark-text);
  --color-text-light: var(--primary-light-text);
  --color-text-lighter: var(--primary-lighter-text);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
