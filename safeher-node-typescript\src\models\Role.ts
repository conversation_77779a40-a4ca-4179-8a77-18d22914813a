import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { RoleAttributes, RoleCreationAttributes } from '../types';

class Role extends Model<RoleAttributes, RoleCreationAttributes> implements RoleAttributes {
  public id!: string;
  public name!: 'admin' | 'guardian' | 'user';
  public description?: string;
  public permissions?: string[];
  public created_at!: Date;
  public updated_at!: Date;

  // Associations
  public static associate(models: any) {
    Role.hasMany(models.User, {
      foreignKey: 'role_id',
      as: 'users'
    });
  }
}

Role.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.ENUM('admin', 'guardian', 'user'),
      allowNull: false,
      unique: true,
      validate: {
        isIn: [['admin', 'guardian', 'user']],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    permissions: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'Role',
    tableName: 'roles',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['name'],
      },
    ],
  }
);

export default Role;
