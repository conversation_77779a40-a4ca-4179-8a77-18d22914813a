import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import Card from '../../components/ui/Card';
import { colors, getTypography, spacing, borderRadius } from '../../lib/design-tokens';

export default function AccountScreen() {
  const [notificationsEnabled, setNotificationsEnabled] = React.useState(true);
  const [locationEnabled, setLocationEnabled] = React.useState(false);

  const menuItems = [
    {
      title: 'Profile Settings',
      icon: 'person-outline',
      onPress: () => {},
    },
    {
      title: 'Privacy & Security',
      icon: 'shield-outline',
      onPress: () => {},
    },
    {
      title: 'Emergency Contacts',
      icon: 'call-outline',
      onPress: () => {},
    },
    {
      title: 'Safety Preferences',
      icon: 'settings-outline',
      onPress: () => {},
    },
  ];

  const supportItems = [
    {
      title: 'Help Center',
      icon: 'help-circle-outline',
      onPress: () => {},
    },
    {
      title: 'Contact Support',
      icon: 'mail-outline',
      onPress: () => {},
    },
    {
      title: 'Report a Bug',
      icon: 'bug-outline',
      onPress: () => {},
    },
    {
      title: 'Terms & Privacy',
      icon: 'document-text-outline',
      onPress: () => {},
    },
  ];

  const renderMenuItem = (item: any, index: number) => (
    <TouchableOpacity key={index} style={styles.menuItem} onPress={item.onPress}>
      <View style={styles.menuItemLeft}>
        <Ionicons name={item.icon} size={24} color={colors.textPrimary} />
        <Text style={styles.menuItemText}>{item.title}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color={colors.textLight} />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Account</Text>
        <TouchableOpacity style={styles.settingsButton}>
          <Ionicons name="settings-outline" size={24} color={colors.primaryWhite} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <Card variant="elevated" style={styles.profileCard}>
          <View style={styles.profileHeader}>
            <View style={styles.profileAvatar}>
              <Text style={styles.profileInitials}>LM</Text>
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>Lilian Martinez</Text>
              <Text style={styles.profileEmail}><EMAIL></Text>
              <View style={styles.verificationBadge}>
                <Ionicons name="checkmark-circle" size={16} color={colors.success} />
                <Text style={styles.verificationText}>Verified</Text>
              </View>
            </View>
            <TouchableOpacity style={styles.editButton}>
              <Ionicons name="pencil" size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </Card>

        {/* Quick Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>12</Text>
            <Text style={styles.statLabel}>Reports</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>85%</Text>
            <Text style={styles.statLabel}>Safety Score</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>24</Text>
            <Text style={styles.statLabel}>Days Active</Text>
          </View>
        </View>

        {/* Settings Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Settings</Text>
          <Card variant="bordered" style={styles.settingsCard}>
            {menuItems.map((item, index) => renderMenuItem(item, index))}
          </Card>
        </View>

        {/* Preferences Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          <Card variant="bordered" style={styles.preferencesCard}>
            <View style={styles.preferenceItem}>
              <View style={styles.preferenceLeft}>
                <Ionicons name="notifications-outline" size={24} color={colors.textPrimary} />
                <View style={styles.preferenceText}>
                  <Text style={styles.preferenceTitle}>Push Notifications</Text>
                  <Text style={styles.preferenceDescription}>
                    Receive alerts for safety updates
                  </Text>
                </View>
              </View>
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
                trackColor={{ false: colors.borderDark, true: colors.primaryLightShade }}
                thumbColor={notificationsEnabled ? colors.primary : colors.textLighter}
              />
            </View>

            <View style={styles.divider} />

            <View style={styles.preferenceItem}>
              <View style={styles.preferenceLeft}>
                <Ionicons name="location-outline" size={24} color={colors.textPrimary} />
                <View style={styles.preferenceText}>
                  <Text style={styles.preferenceTitle}>Location Services</Text>
                  <Text style={styles.preferenceDescription}>
                    Enable for emergency features
                  </Text>
                </View>
              </View>
              <Switch
                value={locationEnabled}
                onValueChange={setLocationEnabled}
                trackColor={{ false: colors.borderDark, true: colors.primaryLightShade }}
                thumbColor={locationEnabled ? colors.primary : colors.textLighter}
              />
            </View>
          </Card>
        </View>

        {/* Support Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          <Card variant="bordered" style={styles.supportCard}>
            {supportItems.map((item, index) => renderMenuItem(item, index))}
          </Card>
        </View>

        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton}>
          <Ionicons name="log-out-outline" size={24} color={colors.error} />
          <Text style={styles.logoutText}>Sign Out</Text>
        </TouchableOpacity>

        {/* App Version */}
        <Text style={styles.versionText}>SafeHer v1.0.0</Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    paddingTop: spacing.lg,
  },
  headerTitle: {
    ...getTypography('title-b-20'),
    color: colors.primaryWhite,
  },
  settingsButton: {
    padding: spacing.sm,
  },
  content: {
    flex: 1,
  },
  profileCard: {
    margin: spacing.lg,
    marginBottom: spacing.md,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileAvatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  profileInitials: {
    ...getTypography('title-b-20'),
    color: colors.primaryWhite,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    ...getTypography('title-b-16'),
    color: colors.textPrimary,
    marginBottom: spacing.xs,
  },
  profileEmail: {
    ...getTypography('paragraph-r-12'),
    color: colors.textLight,
    marginBottom: spacing.xs,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  verificationText: {
    ...getTypography('paragraph-r-10'),
    color: colors.success,
    marginLeft: spacing.xs,
  },
  editButton: {
    padding: spacing.sm,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.background,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    ...getTypography('title-b-20'),
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  statLabel: {
    ...getTypography('paragraph-r-10'),
    color: colors.textLight,
  },
  statDivider: {
    width: 1,
    backgroundColor: colors.borderLight,
    marginVertical: spacing.sm,
  },
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    ...getTypography('title-b-16'),
    color: colors.textPrimary,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  settingsCard: {
    marginHorizontal: spacing.lg,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    ...getTypography('paragraph-m-16'),
    color: colors.textPrimary,
    marginLeft: spacing.md,
  },
  preferencesCard: {
    marginHorizontal: spacing.lg,
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
  },
  preferenceLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  preferenceText: {
    marginLeft: spacing.md,
    flex: 1,
  },
  preferenceTitle: {
    ...getTypography('paragraph-m-16'),
    color: colors.textPrimary,
    marginBottom: spacing.xs,
  },
  preferenceDescription: {
    ...getTypography('paragraph-r-12'),
    color: colors.textLight,
  },
  divider: {
    height: 1,
    backgroundColor: colors.borderLight,
    marginVertical: spacing.sm,
  },
  supportCard: {
    marginHorizontal: spacing.lg,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.error,
  },
  logoutText: {
    ...getTypography('paragraph-m-16'),
    color: colors.error,
    marginLeft: spacing.sm,
  },
  versionText: {
    ...getTypography('paragraph-r-10'),
    color: colors.textLighter,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
});
