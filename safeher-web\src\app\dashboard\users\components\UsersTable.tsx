'use client';

import { useState } from 'react';
import { ChevronRightIcon, EllipsisHorizontalIcon } from '@heroicons/react/24/outline';
import { getTypography, createCardVariant } from '@/lib/design-tokens';

// Mock data for users
const mockUsers = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    role: 'User',
    status: 'Active',
    joinDate: '2024-01-15',
    reportsSubmitted: 12,
    resourcesShared: 5,
    lastActive: '2 hours ago',
    location: 'Nairobi, Kenya',
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    role: 'Guardian',
    status: 'Active',
    joinDate: '2024-02-20',
    reportsSubmitted: 8,
    resourcesShared: 15,
    lastActive: '1 day ago',
    location: 'Lagos, Nigeria',
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    role: 'User',
    status: 'Active',
    joinDate: '2024-03-10',
    reportsSubmitted: 3,
    resourcesShared: 2,
    lastActive: '5 minutes ago',
    location: 'Mumbai, India',
  },
  {
    id: 4,
    name: '<PERSON> Chen',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    role: 'Moderator',
    status: 'Active',
    joinDate: '2023-11-05',
    reportsSubmitted: 25,
    resourcesShared: 30,
    lastActive: '30 minutes ago',
    location: 'Singapore',
  },
  {
    id: 5,
    name: 'Fatima Al-Zahra',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    role: 'User',
    status: 'Inactive',
    joinDate: '2024-01-08',
    reportsSubmitted: 1,
    resourcesShared: 0,
    lastActive: '2 weeks ago',
    location: 'Cairo, Egypt',
  },
  {
    id: 6,
    name: 'Grace Okafor',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    role: 'Guardian',
    status: 'Active',
    joinDate: '2023-12-12',
    reportsSubmitted: 18,
    resourcesShared: 22,
    lastActive: '4 hours ago',
    location: 'Accra, Ghana',
  },
  {
    id: 7,
    name: 'Priya Sharma',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    role: 'User',
    status: 'Active',
    joinDate: '2024-02-28',
    reportsSubmitted: 6,
    resourcesShared: 3,
    lastActive: '1 hour ago',
    location: 'Delhi, India',
  },
  {
    id: 8,
    name: 'Amina Hassan',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    role: 'User',
    status: 'Active',
    joinDate: '2024-03-15',
    reportsSubmitted: 4,
    resourcesShared: 1,
    lastActive: '3 days ago',
    location: 'Khartoum, Sudan',
  },
];

export function UsersTable() {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = 15;

  return (
    <div className="space-y-6">
      {/* Table */}
      <div className={`${createCardVariant('bordered')} overflow-hidden`}>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-border-light">
            <thead className="bg-gray-50">
              <tr>
                <th className={`px-6 py-3 text-left ${getTypography('paragraph-m-12')} text-text-dark uppercase tracking-wider`}>
                  User
                </th>
                <th className={`px-6 py-3 text-left ${getTypography('paragraph-m-12')} text-text-dark uppercase tracking-wider`}>
                  Role
                </th>
                <th className={`px-6 py-3 text-left ${getTypography('paragraph-m-12')} text-text-dark uppercase tracking-wider`}>
                  Status
                </th>
                <th className={`px-6 py-3 text-left ${getTypography('paragraph-m-12')} text-text-dark uppercase tracking-wider`}>
                  Reports
                </th>
                <th className={`px-6 py-3 text-left ${getTypography('paragraph-m-12')} text-text-dark uppercase tracking-wider`}>
                  Resources
                </th>
                <th className={`px-6 py-3 text-left ${getTypography('paragraph-m-12')} text-text-dark uppercase tracking-wider`}>
                  Last Active
                </th>
                <th className={`px-6 py-3 text-left ${getTypography('paragraph-m-12')} text-text-dark uppercase tracking-wider`}>
                  Location
                </th>
                <th className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-border-light">
              {mockUsers.map((user, index) => (
                <tr 
                  key={user.id} 
                  className={`hover:bg-gray-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <img
                        src={user.avatar}
                        alt={user.name}
                        className="w-10 h-10 rounded-full"
                      />
                      <div>
                        <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                          {user.name}
                        </p>
                        <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                          {user.email}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      user.role === 'Guardian' 
                        ? 'bg-secondary/10 text-secondary' 
                        : user.role === 'Moderator'
                        ? 'bg-primary/10 text-primary'
                        : 'bg-gray-100 text-text-dark'
                    }`}>
                      {user.role}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      user.status === 'Active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {user.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`${getTypography('paragraph-r-12')} text-text-dark`}>
                      {user.reportsSubmitted}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`${getTypography('paragraph-r-12')} text-text-dark`}>
                      {user.resourcesShared}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`${getTypography('paragraph-r-12')} text-text-light`}>
                      {user.lastActive}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`${getTypography('paragraph-r-12')} text-text-light`}>
                      {user.location}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button className="p-1 hover:bg-gray-100 rounded">
                        <EllipsisHorizontalIcon className="w-4 h-4 text-text-light" />
                      </button>
                      <button className="p-1 hover:bg-gray-100 rounded">
                        <ChevronRightIcon className="w-4 h-4 text-text-light" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className={`px-4 py-2 text-sm font-medium text-text-light border border-border-light rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          Previous
        </button>
        
        <div className="flex items-center space-x-2">
          {[1, 2, 3, '...', 13, 14, 15].map((page, index) => (
            <button
              key={index}
              onClick={() => typeof page === 'number' && setCurrentPage(page)}
              className={`px-3 py-2 text-sm font-medium rounded-lg ${
                page === currentPage
                  ? 'bg-primary text-white'
                  : 'text-text-light hover:bg-gray-50'
              } ${typeof page !== 'number' ? 'cursor-default' : ''}`}
              disabled={typeof page !== 'number'}
            >
              {page}
            </button>
          ))}
        </div>

        <button
          onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
          className={`px-4 py-2 text-sm font-medium text-text-light border border-border-light rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          Next
        </button>
      </div>
    </div>
  );
}
