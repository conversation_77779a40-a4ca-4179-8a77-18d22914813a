'use client';

import { useState } from 'react';
import { CameraIcon } from '@heroicons/react/24/outline';
import { getTypography, createButtonVariant, createCardVariant } from '@/lib/design-tokens';

export function ProfileSettings() {
  const [formData, setFormData] = useState({
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+254 712 345 678',
    bio: 'Digital safety advocate and community organizer passionate about creating safer online spaces for women.',
    location: 'Nairobi, Kenya',
    website: 'https://sarahjohnson.com',
    twitter: '@sarahjohnson',
    linkedin: 'sarah-johnson',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Profile updated:', formData);
  };

  return (
    <div className={`${createCardVariant('bordered')} p-6 space-y-6`}>
      <div>
        <h2 className={`${getTypography('title-b-18')} text-text-primary mb-2`}>
          Profile Information
        </h2>
        <p className={`${getTypography('paragraph-r-14')} text-text-light`}>
          Update your personal information and how others see you on SafeHer.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Profile Photo */}
        <div>
          <label className={`block ${getTypography('paragraph-m-14')} text-text-primary mb-4`}>
            Profile Photo
          </label>
          <div className="flex items-center space-x-6">
            <div className="relative">
              <img
                src="/api/placeholder/80/80"
                alt="Profile"
                className="w-20 h-20 rounded-full object-cover"
              />
              <button
                type="button"
                className="absolute -bottom-1 -right-1 bg-primary text-white p-2 rounded-full hover:bg-primary-shade transition-colors"
              >
                <CameraIcon className="w-4 h-4" />
              </button>
            </div>
            <div className="space-y-2">
              <button
                type="button"
                className={`${createButtonVariant('secondary')} ${getTypography('paragraph-m-12')}`}
              >
                Change Photo
              </button>
              <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                JPG, PNG up to 5MB
              </p>
            </div>
          </div>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              First Name
            </label>
            <input
              type="text"
              value={formData.firstName}
              onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Last Name
            </label>
            <input
              type="text"
              value={formData.lastName}
              onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Email Address
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              Phone Number
            </label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        <div>
          <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
            Bio
          </label>
          <textarea
            rows={4}
            value={formData.bio}
            onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
            className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Tell us about yourself..."
          />
        </div>

        <div>
          <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
            Location
          </label>
          <input
            type="text"
            value={formData.location}
            onChange={(e) => setFormData({ ...formData, location: e.target.value })}
            className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="City, Country"
          />
        </div>

        {/* Social Links */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Social Links
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
                Website
              </label>
              <input
                type="url"
                value={formData.website}
                onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="https://yourwebsite.com"
              />
            </div>
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
                Twitter
              </label>
              <input
                type="text"
                value={formData.twitter}
                onChange={(e) => setFormData({ ...formData, twitter: e.target.value })}
                className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="@username"
              />
            </div>
          </div>
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
              LinkedIn
            </label>
            <input
              type="text"
              value={formData.linkedin}
              onChange={(e) => setFormData({ ...formData, linkedin: e.target.value })}
              className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="linkedin-username"
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-border-light">
          <button
            type="button"
            className={`px-6 py-2 ${getTypography('paragraph-m-12')} text-text-light border border-border-light rounded-lg hover:bg-gray-50 transition-colors`}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={`${createButtonVariant('primary')} ${getTypography('paragraph-m-12')}`}
          >
            Save Changes
          </button>
        </div>
      </form>
    </div>
  );
}
