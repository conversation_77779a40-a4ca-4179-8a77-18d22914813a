import { Response } from 'express';
import { Op } from 'sequelize';
import { User, Role, Guardian } from '../models';
import { CustomError, asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest, ApiResponse, PaginatedResponse } from '../types';

export const getGuardians = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const {
    page = '1',
    limit = '10',
    search = '',
    status = '',
    sort = 'created_at',
    order = 'DESC'
  } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  // Build where clause
  const whereClause: any = {};
  
  if (status && status !== 'all') {
    whereClause.status = status;
  }

  // Build user search clause
  const userWhereClause: any = {};
  if (search) {
    userWhereClause[Op.or] = [
      { name: { [Op.like]: `%${search}%` } },
      { email: { [Op.like]: `%${search}%` } },
    ];
  }

  const { count, rows: guardians } = await Guardian.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'user',
        where: userWhereClause,
        attributes: ['id', 'name', 'email', 'avatar', 'created_at'],
      },
      {
        model: User,
        as: 'approver',
        attributes: ['id', 'name', 'email'],
        required: false,
      },
    ],
    limit: limitNum,
    offset,
    order: [[sort as string, order as string]],
  });

  const totalPages = Math.ceil(count / limitNum);

  const response: PaginatedResponse<any> = {
    data: guardians.map(guardian => ({
      id: guardian.id,
      user: guardian.user,
      designation: guardian.designation,
      organization: guardian.organization,
      bio: guardian.bio,
      expertiseAreas: guardian.expertise_areas,
      status: guardian.status,
      resourcesCount: guardian.resources_count,
      reportsCount: guardian.reports_count,
      approver: guardian.approver,
      approvedAt: guardian.approved_at,
      createdAt: guardian.created_at,
    })),
    pagination: {
      page: pageNum,
      limit: limitNum,
      total: count,
      totalPages,
      hasNext: pageNum < totalPages,
      hasPrev: pageNum > 1,
    },
  };

  res.status(200).json({
    success: true,
    ...response,
  });
});

export const approveGuardian = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const approverId = req.user!.id;

  const guardian = await Guardian.findByPk(id, {
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email'],
      },
    ],
  });

  if (!guardian) {
    throw new CustomError('Guardian not found', 404);
  }

  if (guardian.status !== 'pending') {
    throw new CustomError('Guardian application is not pending', 400);
  }

  // Get guardian role
  const guardianRole = await Role.findOne({ where: { name: 'guardian' } });
  if (!guardianRole) {
    throw new CustomError('Guardian role not found', 500);
  }

  // Update guardian status
  await guardian.update({
    status: 'approved',
    approved_by: approverId,
    approved_at: new Date(),
  });

  // Update user role
  await User.update(
    { role_id: guardianRole.id },
    { where: { id: guardian.user_id } }
  );

  const response: ApiResponse = {
    success: true,
    message: 'Guardian approved successfully',
    data: {
      guardian: {
        id: guardian.id,
        user: guardian.user,
        status: guardian.status,
        approvedAt: guardian.approved_at,
      },
    },
  };

  res.status(200).json(response);
});

export const rejectGuardian = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { reason } = req.body;
  const approverId = req.user!.id;

  const guardian = await Guardian.findByPk(id, {
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email'],
      },
    ],
  });

  if (!guardian) {
    throw new CustomError('Guardian not found', 404);
  }

  if (guardian.status !== 'pending') {
    throw new CustomError('Guardian application is not pending', 400);
  }

  // Update guardian status
  await guardian.update({
    status: 'rejected',
    approved_by: approverId,
    approved_at: new Date(),
  });

  const response: ApiResponse = {
    success: true,
    message: 'Guardian application rejected',
    data: {
      guardian: {
        id: guardian.id,
        user: guardian.user,
        status: guardian.status,
        reason,
      },
    },
  };

  res.status(200).json(response);
});

export const revokeGuardian = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { reason } = req.body;
  const approverId = req.user!.id;

  const guardian = await Guardian.findByPk(id, {
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email'],
      },
    ],
  });

  if (!guardian) {
    throw new CustomError('Guardian not found', 404);
  }

  if (guardian.status !== 'approved') {
    throw new CustomError('Guardian is not currently approved', 400);
  }

  // Get user role
  const userRole = await Role.findOne({ where: { name: 'user' } });
  if (!userRole) {
    throw new CustomError('User role not found', 500);
  }

  // Update guardian status
  await guardian.update({
    status: 'suspended',
    approved_by: approverId,
    approved_at: new Date(),
  });

  // Revert user role back to user
  await User.update(
    { role_id: userRole.id },
    { where: { id: guardian.user_id } }
  );

  const response: ApiResponse = {
    success: true,
    message: 'Guardian access revoked successfully',
    data: {
      guardian: {
        id: guardian.id,
        user: guardian.user,
        status: guardian.status,
        reason,
      },
    },
  };

  res.status(200).json(response);
});

export const getGuardianById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const guardian = await Guardian.findByPk(id, {
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'avatar', 'created_at'],
      },
      {
        model: User,
        as: 'approver',
        attributes: ['id', 'name', 'email'],
        required: false,
      },
    ],
  });

  if (!guardian) {
    throw new CustomError('Guardian not found', 404);
  }

  const response: ApiResponse = {
    success: true,
    data: {
      guardian: {
        id: guardian.id,
        user: guardian.user,
        designation: guardian.designation,
        organization: guardian.organization,
        bio: guardian.bio,
        expertiseAreas: guardian.expertise_areas,
        status: guardian.status,
        resourcesCount: guardian.resources_count,
        reportsCount: guardian.reports_count,
        approver: guardian.approver,
        approvedAt: guardian.approved_at,
        createdAt: guardian.created_at,
      },
    },
  };

  res.status(200).json(response);
});
