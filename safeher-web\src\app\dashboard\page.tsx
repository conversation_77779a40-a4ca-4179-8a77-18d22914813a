import DashboardLayout from '@/components/global/layout/DashboardLayout';
import StatsCards from './components/StatsCards';
import IncidentsChart from './components/IncidentsChart';
import GuardiansWidget from './components/GuardiansWidget';

export default function DashboardPage() {
  return (
    <DashboardLayout title="Dashboard">
      <div className="space-y-6">
        <StatsCards />
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <IncidentsChart />
          <GuardiansWidget />
        </div>
      </div>
    </DashboardLayout>
  );
}
