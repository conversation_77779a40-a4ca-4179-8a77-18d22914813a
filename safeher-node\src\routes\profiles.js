const express = require('express');
const {
  getProfile,
  updateProfile,
  updatePrivacySettings,
  updateNotificationPreferences,
  deleteProfile
} = require('../controllers/profileController');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * /api/profiles:
 *   get:
 *     summary: Get current user profile
 *     tags: [Profiles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Profile retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/', authenticate, getProfile);

/**
 * @swagger
 * /api/profiles:
 *   put:
 *     summary: Update user profile
 *     tags: [Profiles]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               avatar:
 *                 type: string
 *                 format: uri
 *               bio:
 *                 type: string
 *                 maxLength: 1000
 *               phone:
 *                 type: string
 *                 maxLength: 20
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *               gender:
 *                 type: string
 *                 enum: [female, male, non-binary, prefer-not-to-say]
 *               location:
 *                 type: string
 *                 maxLength: 255
 *               website:
 *                 type: string
 *                 format: uri
 *               socialLinks:
 *                 type: object
 *               privacySettings:
 *                 type: object
 *                 properties:
 *                   show_email:
 *                     type: boolean
 *                   show_phone:
 *                     type: boolean
 *                   show_location:
 *                     type: boolean
 *                   show_date_of_birth:
 *                     type: boolean
 *               notificationPreferences:
 *                 type: object
 *                 properties:
 *                   email_notifications:
 *                     type: boolean
 *                   incident_updates:
 *                     type: boolean
 *                   resource_updates:
 *                     type: boolean
 *                   marketing_emails:
 *                     type: boolean
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Validation error
 */
router.put('/', authenticate, updateProfile);

/**
 * @swagger
 * /api/profiles/privacy:
 *   put:
 *     summary: Update privacy settings
 *     tags: [Profiles]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - privacySettings
 *             properties:
 *               privacySettings:
 *                 type: object
 *                 properties:
 *                   show_email:
 *                     type: boolean
 *                   show_phone:
 *                     type: boolean
 *                   show_location:
 *                     type: boolean
 *                   show_date_of_birth:
 *                     type: boolean
 *     responses:
 *       200:
 *         description: Privacy settings updated successfully
 *       401:
 *         description: Unauthorized
 */
router.put('/privacy', authenticate, updatePrivacySettings);

/**
 * @swagger
 * /api/profiles/notifications:
 *   put:
 *     summary: Update notification preferences
 *     tags: [Profiles]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - notificationPreferences
 *             properties:
 *               notificationPreferences:
 *                 type: object
 *                 properties:
 *                   email_notifications:
 *                     type: boolean
 *                   incident_updates:
 *                     type: boolean
 *                   resource_updates:
 *                     type: boolean
 *                   marketing_emails:
 *                     type: boolean
 *     responses:
 *       200:
 *         description: Notification preferences updated successfully
 *       401:
 *         description: Unauthorized
 */
router.put('/notifications', authenticate, updateNotificationPreferences);

/**
 * @swagger
 * /api/profiles:
 *   delete:
 *     summary: Delete user profile
 *     tags: [Profiles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Profile deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Profile not found
 */
router.delete('/', authenticate, deleteProfile);

module.exports = router;
