'use client';

import { XMarkIcon, EyeIcon } from '@heroicons/react/24/outline';
import { getTypography, createButtonVariant } from '@/lib/design-tokens';

interface ResourcePreviewModalProps {
  resource: {
    id: number;
    title: string;
    category: string;
    date: string;
    excerpt: string;
    readTime: string;
    author: string;
  } | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ResourcePreviewModal({ resource, isOpen, onClose }: ResourcePreviewModalProps) {
  if (!isOpen || !resource) return null;

  // Mock full content for the preview
  const fullContent = `
    <h2>The Digital Playground</h2>
    <p>In today's interconnected world, the internet serves as a vast playground for children. It offers them opportunities to learn, communicate, create, and explore the world in unprecedented ways. However, this digital realm also harbors lurking dangers that can leave our youngest generation vulnerable. It's our responsibility as a society to ensure child safety online.</p>
    
    <img src="/api/placeholder/600/300" alt="Children using computers" style="width: 100%; height: 300px; object-fit: cover; border-radius: 8px; margin: 20px 0;" />
    
    <h3>No Distinction Between Realms</h3>
    <p>For many children, there exists no clear boundary between their online and offline lives. They seamlessly transition from the physical to the digital world, making it imperative that we address online risks comprehensively. Online sexual abuse is one such peril that children may encounter on the computer. It encompasses a range of manipulative, exploitative, and coercive actions that aim to involve children in sexual activities.</p>
    
    <p>This comprehensive guide explores the various aspects of ensuring child safety in the digital age, providing parents, educators, and caregivers with the knowledge and tools necessary to protect children from online threats while allowing them to benefit from the positive aspects of digital technology.</p>
    
    <h3>Understanding Online Risks</h3>
    <p>The digital landscape presents numerous challenges for child safety. From cyberbullying to inappropriate content exposure, children face various risks that require our attention and proactive measures. Understanding these risks is the first step in creating a safer online environment for our children.</p>
    
    <p>By working together as a community, we can create digital spaces that nurture learning, creativity, and positive social interaction while protecting children from harm. This requires ongoing education, open communication, and the implementation of appropriate safety measures both at home and in educational settings.</p>
  `;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border-light">
          <div className="flex items-center space-x-3">
            <EyeIcon className="w-5 h-5 text-primary" />
            <h2 className={`${getTypography('title-b-20')} text-text-primary`}>
              Preview
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <XMarkIcon className="w-5 h-5 text-text-light" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Article Header */}
          <div className="space-y-4">
            {/* Category and Date */}
            <div className="flex items-center justify-between">
              <span className={`px-3 py-1 bg-primary/10 text-primary rounded-full ${getTypography('paragraph-r-10')}`}>
                {resource.category}
              </span>
              <span className={`${getTypography('paragraph-r-10')} text-text-light`}>
                {resource.date}
              </span>
            </div>

            {/* Title */}
            <h1 className={`${getTypography('title-bold-64')} text-text-primary leading-tight`} style={{ fontSize: '32px', lineHeight: '40px' }}>
              {resource.title}
            </h1>

            {/* Meta Info */}
            <div className="flex items-center space-x-6 text-text-light">
              <span className={`${getTypography('paragraph-r-12')}`}>
                By {resource.author}
              </span>
              <span className={`${getTypography('paragraph-r-12')}`}>
                {resource.readTime}
              </span>
            </div>

            {/* Excerpt */}
            <p className={`${getTypography('paragraph-r-20')} text-text-dark leading-relaxed`}>
              {resource.excerpt}
            </p>
          </div>

          {/* Article Content */}
          <div 
            className={`prose prose-lg max-w-none ${getTypography('paragraph-r-16')} text-text-dark`}
            dangerouslySetInnerHTML={{ __html: fullContent }}
            style={{
              lineHeight: '1.7',
            }}
          />

          {/* Article Footer */}
          <div className="pt-6 border-t border-border-light">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className={`${getTypography('paragraph-r-12')} text-text-light`}>
                  Published on {resource.date}
                </span>
                <span className={`${getTypography('paragraph-r-12')} text-text-light`}>
                  •
                </span>
                <span className={`${getTypography('paragraph-r-12')} text-text-light`}>
                  {resource.category}
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <button className={`${getTypography('link-r-12')} text-primary hover:text-primary-shade`}>
                  Share
                </button>
                <button className={`${getTypography('link-r-12')} text-primary hover:text-primary-shade`}>
                  Save
                </button>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-4 pt-4 border-t border-border-light">
            <button
              onClick={onClose}
              className={`px-6 py-2 ${getTypography('paragraph-m-12')} text-text-light border border-border-light rounded-lg hover:bg-gray-50 transition-colors`}
            >
              Close
            </button>
            <button
              className={`${createButtonVariant('primary')} ${getTypography('paragraph-m-12')}`}
            >
              Read Full Article
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
