# SafeHer Mobile App

A React Native mobile application built with Expo for women's online safety.

## Features

### Authentication
- **Register Screen**: Create account with full name, email, password
- **Login Screen**: Sign in with email/password
- **Social Login**: Google and Facebook integration (placeholder)
- **Terms & Privacy**: Agreement checkbox and links

### Main Application
- **Bottom Tab Navigation**: Dashboard, Incidents, Resources, Account
- **Dashboard**: User greeting, safety insights, quick actions, recent activity
- **Incidents**: Community reports, search/filter, tabs for different report types
- **Resources**: Safety guides, legal help, support groups, emergency contacts
- **Account**: Profile management, settings, preferences, support

## Design System

The app uses a comprehensive design system based on Figma design tokens:

### Colors
- Primary: `#cf1884` (SafeHer pink)
- Secondary: `#3364b3` (Blue)
- Various shades and text colors

### Typography
- **Source Sans Pro**: Titles and headings
- **Nunito**: Paragraphs and body text
- **Montserrat**: Links and buttons

### Components
- **Button**: Multiple variants (primary, secondary, outline, ghost)
- **Input**: Form inputs with icons, validation, password toggle
- **Card**: Container component with elevation and border variants

## Project Structure

```
src/
├── components/
│   └── ui/
│       ├── Button.tsx
│       ├── Input.tsx
│       └── Card.tsx
├── lib/
│   └── design-tokens.ts
├── navigation/
│   ├── AuthNavigator.tsx
│   └── MainNavigator.tsx
└── screens/
    ├── auth/
    │   ├── LoginScreen.tsx
    │   └── RegisterScreen.tsx
    └── main/
        ├── DashboardScreen.tsx
        ├── IncidentsScreen.tsx
        ├── ResourcesScreen.tsx
        └── AccountScreen.tsx
```

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start the development server**:
   ```bash
   npm start
   ```

3. **Run on device/simulator**:
   ```bash
   npm run ios     # iOS simulator
   npm run android # Android emulator
   ```

## Dependencies

- **React Navigation**: Navigation library with native stack and bottom tabs
- **Expo Vector Icons**: Icon library for UI elements
- **React Native Safe Area Context**: Safe area handling
- **TypeScript**: Type safety and development experience

## Design Implementation

The app closely follows the provided Figma designs:

### Register Screen
- Pink header with back button
- Form fields with proper validation
- Social login buttons
- Terms and conditions checkbox

### Dashboard Screen
- Personalized greeting ("Hey, Lilian")
- Insights section with safety score
- Article cards with placeholders
- Quick action buttons
- Recent activity feed

### Incidents Screen
- Search bar with filter option
- Tab navigation (Community, New Reports, My Reports)
- Incident cards with user info, content, and actions
- Floating action button for new reports

## Next Steps

1. **Backend Integration**: Connect to SafeHer API endpoints
2. **Authentication**: Implement real authentication flow
3. **Data Management**: Add state management (Redux/Context)
4. **Push Notifications**: Implement notification system
5. **Offline Support**: Add offline capabilities
6. **Testing**: Add unit and integration tests
7. **Performance**: Optimize for production

## Development Notes

- All screens are responsive and follow mobile-first design principles
- Components use the design token system for consistency
- TypeScript is used throughout for type safety
- The app structure supports easy scaling and feature additions
