'use client';

import { useState } from 'react';
import { ExclamationTriangleIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import { getTypography, createButtonVariant, createCardVariant } from '@/lib/design-tokens';

export function AccountSettings() {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');

  const handleExportData = () => {
    console.log('Exporting user data...');
    // Simulate data export
    alert('Your data export has been initiated. You will receive an email when it\'s ready.');
  };

  const handleDeleteAccount = () => {
    if (deleteConfirmText === 'DELETE') {
      console.log('Account deletion requested');
      alert('Account deletion request submitted. You will receive a confirmation email.');
      setShowDeleteConfirm(false);
      setDeleteConfirmText('');
    }
  };

  return (
    <div className="space-y-6">
      {/* Account Information */}
      <div className={`${createCardVariant('bordered')} p-6 space-y-6`}>
        <div>
          <h2 className={`${getTypography('title-b-18')} text-text-primary mb-2`}>
            Account Information
          </h2>
          <p className={`${getTypography('paragraph-r-14')} text-text-light`}>
            View and manage your account details.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-1`}>
                Account ID
              </label>
              <p className={`${getTypography('paragraph-r-12')} text-text-dark font-mono bg-gray-50 p-2 rounded`}>
                USR-2024-001234
              </p>
            </div>
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-1`}>
                Member Since
              </label>
              <p className={`${getTypography('paragraph-r-12')} text-text-dark`}>
                January 15, 2024
              </p>
            </div>
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-1`}>
                Account Type
              </label>
              <span className="inline-flex px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium">
                Standard User
              </span>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-1`}>
                Last Login
              </label>
              <p className={`${getTypography('paragraph-r-12')} text-text-dark`}>
                Today at 2:30 PM
              </p>
            </div>
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-1`}>
                Reports Submitted
              </label>
              <p className={`${getTypography('paragraph-r-12')} text-text-dark`}>
                12 reports
              </p>
            </div>
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-1`}>
                Resources Shared
              </label>
              <p className={`${getTypography('paragraph-r-12')} text-text-dark`}>
                5 resources
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Data Management */}
      <div className={`${createCardVariant('bordered')} p-6 space-y-6`}>
        <div>
          <h2 className={`${getTypography('title-b-18')} text-text-primary mb-2`}>
            Data Management
          </h2>
          <p className={`${getTypography('paragraph-r-14')} text-text-light`}>
            Control your personal data and account information.
          </p>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 border border-border-light rounded-lg">
            <div className="flex items-center space-x-3">
              <ArrowDownTrayIcon className="w-5 h-5 text-text-light" />
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Export Your Data
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Download a copy of all your SafeHer data
                </p>
              </div>
            </div>
            <button
              onClick={handleExportData}
              className={`${createButtonVariant('secondary')} ${getTypography('paragraph-m-12')}`}
            >
              Export Data
            </button>
          </div>

          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className={`${getTypography('paragraph-m-12')} text-blue-800 mb-2`}>
              Data Retention Policy
            </p>
            <p className={`${getTypography('paragraph-r-10')} text-blue-600`}>
              Your data is retained according to our privacy policy. Reports and resources may be kept for community safety purposes even after account deletion, but will be anonymized.
            </p>
          </div>
        </div>
      </div>

      {/* Account Actions */}
      <div className={`${createCardVariant('bordered')} p-6 space-y-6`}>
        <div>
          <h2 className={`${getTypography('title-b-18')} text-text-primary mb-2`}>
            Account Actions
          </h2>
          <p className={`${getTypography('paragraph-r-14')} text-text-light`}>
            Manage your account status and access.
          </p>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 border border-border-light rounded-lg">
            <div>
              <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                Deactivate Account
              </p>
              <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                Temporarily disable your account (can be reactivated)
              </p>
            </div>
            <button
              className={`px-4 py-2 text-orange-600 border border-orange-300 rounded-lg hover:bg-orange-50 transition-colors ${getTypography('paragraph-m-12')}`}
            >
              Deactivate
            </button>
          </div>

          <div className="border border-red-200 rounded-lg">
            <div className="flex items-center justify-between p-4">
              <div className="flex items-center space-x-3">
                <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />
                <div>
                  <p className={`${getTypography('paragraph-m-12')} text-red-700`}>
                    Delete Account
                  </p>
                  <p className={`${getTypography('paragraph-r-10')} text-red-600`}>
                    Permanently delete your account and all associated data
                  </p>
                </div>
              </div>
              <button
                onClick={() => setShowDeleteConfirm(true)}
                className={`px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors ${getTypography('paragraph-m-12')}`}
              >
                Delete Account
              </button>
            </div>

            {showDeleteConfirm && (
              <div className="border-t border-red-200 p-4 bg-red-50">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mt-0.5" />
                    <div>
                      <p className={`${getTypography('paragraph-b-12')} text-red-800 mb-2`}>
                        This action cannot be undone
                      </p>
                      <ul className={`${getTypography('paragraph-r-10')} text-red-700 space-y-1 list-disc list-inside`}>
                        <li>Your profile and personal information will be permanently deleted</li>
                        <li>Your reports may be kept for community safety but will be anonymized</li>
                        <li>You will lose access to all SafeHer services</li>
                        <li>This action cannot be reversed</li>
                      </ul>
                    </div>
                  </div>

                  <div>
                    <label className={`block ${getTypography('paragraph-m-12')} text-red-800 mb-2`}>
                      Type "DELETE" to confirm:
                    </label>
                    <input
                      type="text"
                      value={deleteConfirmText}
                      onChange={(e) => setDeleteConfirmText(e.target.value)}
                      className="w-full px-3 py-2 border border-red-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      placeholder="Type DELETE to confirm"
                    />
                  </div>

                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => {
                        setShowDeleteConfirm(false);
                        setDeleteConfirmText('');
                      }}
                      className={`px-4 py-2 text-text-dark border border-border-light rounded-lg hover:bg-gray-50 transition-colors ${getTypography('paragraph-m-12')}`}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleDeleteAccount}
                      disabled={deleteConfirmText !== 'DELETE'}
                      className={`px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${getTypography('paragraph-m-12')}`}
                    >
                      Delete My Account
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
