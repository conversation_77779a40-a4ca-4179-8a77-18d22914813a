const { Profile, User } = require('../models');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');

const getProfile = asyncHandler(async (req, res) => {
  const profile = await Profile.findOne({
    where: { user_id: req.user.id },
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email']
      }
    ]
  });

  if (!profile) {
    // Create default profile if it doesn't exist
    const newProfile = await Profile.create({
      user_id: req.user.id,
      privacy_settings: {
        show_email: false,
        show_phone: false,
        show_location: false,
        show_date_of_birth: false
      },
      notification_preferences: {
        email_notifications: true,
        incident_updates: true,
        resource_updates: false,
        marketing_emails: false
      }
    });

    const response = {
      success: true,
      data: {
        profile: {
          id: newProfile.id,
          userId: newProfile.user_id,
          avatar: newProfile.avatar,
          bio: newProfile.bio,
          phone: newProfile.phone,
          dateOfBirth: newProfile.date_of_birth,
          gender: newProfile.gender,
          location: newProfile.location,
          website: newProfile.website,
          socialLinks: newProfile.social_links,
          privacySettings: newProfile.privacy_settings,
          notificationPreferences: newProfile.notification_preferences,
          createdAt: newProfile.created_at,
          updatedAt: newProfile.updated_at
        }
      }
    };

    return res.status(200).json(response);
  }

  const response = {
    success: true,
    data: {
      profile: {
        id: profile.id,
        userId: profile.user_id,
        avatar: profile.avatar,
        bio: profile.bio,
        phone: profile.phone,
        dateOfBirth: profile.date_of_birth,
        gender: profile.gender,
        location: profile.location,
        website: profile.website,
        socialLinks: profile.social_links,
        privacySettings: profile.privacy_settings,
        notificationPreferences: profile.notification_preferences,
        user: profile.user,
        createdAt: profile.created_at,
        updatedAt: profile.updated_at
      }
    }
  };

  res.status(200).json(response);
});

const updateProfile = asyncHandler(async (req, res) => {
  const {
    avatar,
    bio,
    phone,
    dateOfBirth,
    gender,
    location,
    website,
    socialLinks,
    privacySettings,
    notificationPreferences
  } = req.body;

  let profile = await Profile.findOne({
    where: { user_id: req.user.id }
  });

  if (!profile) {
    // Create profile if it doesn't exist
    profile = await Profile.create({
      user_id: req.user.id,
      avatar,
      bio,
      phone,
      date_of_birth: dateOfBirth,
      gender,
      location,
      website,
      social_links: socialLinks || {},
      privacy_settings: privacySettings || {
        show_email: false,
        show_phone: false,
        show_location: false,
        show_date_of_birth: false
      },
      notification_preferences: notificationPreferences || {
        email_notifications: true,
        incident_updates: true,
        resource_updates: false,
        marketing_emails: false
      }
    });
  } else {
    // Update existing profile
    const updateData = {};
    if (avatar !== undefined) updateData.avatar = avatar;
    if (bio !== undefined) updateData.bio = bio;
    if (phone !== undefined) updateData.phone = phone;
    if (dateOfBirth !== undefined) updateData.date_of_birth = dateOfBirth;
    if (gender !== undefined) updateData.gender = gender;
    if (location !== undefined) updateData.location = location;
    if (website !== undefined) updateData.website = website;
    if (socialLinks !== undefined) updateData.social_links = socialLinks;
    if (privacySettings !== undefined) updateData.privacy_settings = privacySettings;
    if (notificationPreferences !== undefined) updateData.notification_preferences = notificationPreferences;

    await profile.update(updateData);
  }

  const response = {
    success: true,
    message: 'Profile updated successfully',
    data: {
      profile: {
        id: profile.id,
        userId: profile.user_id,
        avatar: profile.avatar,
        bio: profile.bio,
        phone: profile.phone,
        dateOfBirth: profile.date_of_birth,
        gender: profile.gender,
        location: profile.location,
        website: profile.website,
        socialLinks: profile.social_links,
        privacySettings: profile.privacy_settings,
        notificationPreferences: profile.notification_preferences,
        updatedAt: profile.updated_at
      }
    }
  };

  res.status(200).json(response);
});

const updatePrivacySettings = asyncHandler(async (req, res) => {
  const { privacySettings } = req.body;

  let profile = await Profile.findOne({
    where: { user_id: req.user.id }
  });

  if (!profile) {
    // Create profile with privacy settings if it doesn't exist
    profile = await Profile.create({
      user_id: req.user.id,
      privacy_settings: privacySettings
    });
  } else {
    await profile.update({ privacy_settings: privacySettings });
  }

  const response = {
    success: true,
    message: 'Privacy settings updated successfully',
    data: {
      privacySettings: profile.privacy_settings
    }
  };

  res.status(200).json(response);
});

const updateNotificationPreferences = asyncHandler(async (req, res) => {
  const { notificationPreferences } = req.body;

  let profile = await Profile.findOne({
    where: { user_id: req.user.id }
  });

  if (!profile) {
    // Create profile with notification preferences if it doesn't exist
    profile = await Profile.create({
      user_id: req.user.id,
      notification_preferences: notificationPreferences
    });
  } else {
    await profile.update({ notification_preferences: notificationPreferences });
  }

  const response = {
    success: true,
    message: 'Notification preferences updated successfully',
    data: {
      notificationPreferences: profile.notification_preferences
    }
  };

  res.status(200).json(response);
});

const deleteProfile = asyncHandler(async (req, res) => {
  const profile = await Profile.findOne({
    where: { user_id: req.user.id }
  });

  if (!profile) {
    throw new CustomError('Profile not found', 404);
  }

  await profile.destroy();

  const response = {
    success: true,
    message: 'Profile deleted successfully'
  };

  res.status(200).json(response);
});

module.exports = {
  getProfile,
  updateProfile,
  updatePrivacySettings,
  updateNotificationPreferences,
  deleteProfile
};
