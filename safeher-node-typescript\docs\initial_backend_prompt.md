# SafeHer Backend Implementation Prompt

## 1. Project Context

You are implementing the **SafeHer** Node.js backend (Express + JavaScript or TypeScript) for the MVP of SafeOnline Women Kenya’s SafeHer system.

Use the conversation history & the pages we generated on the web to capture anything missing.  
The **Figma screens (attached separately)** are the single source of truth for UI flows, field names, validations, and data relationships.

### 1.1 Initial Requirements (for reference)
> **Overview:**  
> SafeHer is a mobile‑first application to curb online violence against women through:
> - anti‑cyberbullying algorithms
> - community reporting system
> - guardian network
> - privacy settings
> - educational resources
> - real‑time monitoring
> - anonymous submissions
>
> See `SafeHer – Mobile App Idea.pdf` for initial spec.

### 1.2 Figma Screens
**Web admin screens include:**
- Dashboard (real-time monitoring)
- Incidences
- Resources
- Guardians list (table view)
- Users
- Settings
- Log out

> **Note:** Use the attached Figma exports and since we set up the web and mobile using those images you can checkout their layouts if we did it consistently. (`Admin Guardians.png`,) as the authoritative source for field names, layouts, & data.

---

## 2. API & Data Model Requirements

### 2.1 Data Models (Sequelize)

#### 🔹 Roles
- `Role`
  - `id` (PK)
  - `name` (`admin` | `guardian` | `user`)

#### 🔹 Users
- `User`
  - `id` (PK)
  - `name`
  - `email` (unique)
  - `passwordHash`
  - `roleId` (FK → `Role`)
  - `createdAt`
  - `updatedAt`

#### 🔹 Incidences
- `Incidence`
  - `id` (PK)
  - `reporterId` (FK → `User`)
  - `guardianId` (FK → `User`) // guardian assigned to handle it
  - `type`
  - `description`
  - `status` (`open` | `in_review` | `closed`)
  - `createdAt`

#### 🔹 Reports
- `Report`
  - `id` (PK)
  - `incidenceId` (FK → `Incidence`)
  - `reporterId` (FK → `User`)
  - `comments`
  - `anonymous` (boolean)
  - `createdAt`

#### 🔹 Resources
- `Resource`
  - `id` (PK)
  - `title`
  - `content`
  - `link`
  - `guardianId` (FK → `User`) // guardian or admin who created it
  - `createdAt`

---

### 2.2 Endpoints

#### Auth & Users
- `POST /api/auth/register` – new user registration  
  - default role = `user` unless registering as guardian (see below).
- `POST /api/auth/login` – email & password → JWT  
- `POST /api/auth/register-guardian` – submit guardian application (requires admin approval)
- `GET /api/users/me` – return current user profile

#### Guardians
- `GET /api/guardians` – paginated list of guardians (with search by name, filter by designation)
- `PATCH /api/guardians/:id/approve` – admin approves guardian request
- `PATCH /api/guardians/:id/revoke` – admin revokes guardian access

#### Incidences & Reports
- `POST /api/incidences` – create new incidence (can be anonymous)
- `GET /api/incidences` – list (admin/guardian access)
- `PATCH /api/incidences/:id` – update status
- `POST /api/incidences/:id/reports` – add comments or follow-up

#### Resources
- `GET /api/resources` – list educational resources
- `POST /api/resources` – create resource (guardian/admin only)
- `DELETE /api/resources/:id` – remove resource

---

## 3. Technical Requirements

1. **JavaScript (or TypeScript if you prefer)**  
2. **Sequelize** for data models & migrations ( MySQL)  
3. **Zod** for request validation  
4. **JWT** authentication (`Authorization: Bearer <token>`)  
5. **Express** with CORS & centralized error handling  
6. **Swagger / OpenAPI** docs at `/api/docs` (optional but preferred)  
7. **Testing** via Jest + Supertest on key modules (≥ 80% coverage)

---

## 4. Tasks & Acceptance Criteria

### ✅ Database & Migrations
- Sequelize models for `Role`, `User`, `Incidence`, `Report`, `Resource`
- Foreign keys & constraints correctly set up
- Migrations & seeders to:
  - create default roles (`admin`, `guardian`, `user`)
  - create initial admin user

### ✅ Auth Module
- Email/password login, bcrypt hashing, JWT issuing
- Social login support (can be stubbed with OAuth placeholders)
- Guard middleware for:
  - `isAuthenticated`
  - `isAdmin`, `isGuardian`
- Guardian routes protected so pending guardians cannot view incidence dashboard until approved

### ✅ CRUD APIs
- All endpoints implemented per above
- Filters & pagination (`limit`, `offset`, `search`)
- Robust validation — returns `400` with helpful messages on failure

### ✅ Documentation & Tests
- Auto OpenAPI/Swagger doc on `/api/docs`
- Jest tests on services & routes, ≥ 80% coverage
- Test file-based SQLite DB or dedicated test schema

---

## 5. Notes & Attachments

- **Attach your Figma export images & PDFs** in the same directory so agents or developers can look up UI flow & payload structure.
- PDF `SafeHer – Mobile App Idea.pdf` is secondary, Figma is primary i,e what we used to design the mobile and web.
- Use conversation references to see:
  - dashboard designs (guards vs admin views)
  - settings & users flows

---

## 🚀 Deliverable

A well structured project with :

**README must include:**
- Setup instructions
- How to run migrations
- How to seed roles & first user
- How to start dev server
- How to run tests

---

## ✅ Optional Enhancements (MVP+)

- Notification email when a guardian is approved.
- Rate-limiting / brute-force protection on login endpoints.



