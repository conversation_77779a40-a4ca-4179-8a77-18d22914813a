import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import Card from '../../components/ui/Card';
import { colors, getTypography, spacing, borderRadius } from '../../lib/design-tokens';

export default function DashboardScreen() {
  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.greeting}>Hey, Lilian</Text>
            <Text style={styles.subGreeting}>Stay safe online today</Text>
          </View>
          <TouchableOpacity style={styles.notificationButton}>
            <Ionicons name="notifications-outline" size={24} color={colors.primaryWhite} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Insights Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Insights</Text>
          
          <Card variant="elevated" style={styles.insightCard}>
            <View style={styles.insightHeader}>
              <Text style={styles.insightTitle}>Safety Score</Text>
              <Text style={styles.insightScore}>85%</Text>
            </View>
            <Text style={styles.insightDescription}>
              Your online safety practices are good. Keep it up!
            </Text>
          </Card>

          {/* Article Cards */}
          <View style={styles.articleGrid}>
            <Card variant="bordered" style={styles.articleCard}>
              <View style={styles.articleImagePlaceholder} />
              <Text style={styles.articleTitle}>Online Safety Tips</Text>
              <Text style={styles.articleDescription}>
                Learn essential tips to protect yourself online
              </Text>
            </Card>

            <Card variant="bordered" style={styles.articleCard}>
              <View style={styles.articleImagePlaceholder} />
              <Text style={styles.articleTitle}>Recognizing Threats</Text>
              <Text style={styles.articleDescription}>
                How to identify potential online threats
              </Text>
            </Card>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.quickAction}>
              <View style={[styles.quickActionIcon, { backgroundColor: colors.primary }]}>
                <Ionicons name="alert-circle" size={24} color={colors.primaryWhite} />
              </View>
              <Text style={styles.quickActionText}>Report Incident</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.quickAction}>
              <View style={[styles.quickActionIcon, { backgroundColor: colors.secondary }]}>
                <Ionicons name="shield-checkmark" size={24} color={colors.primaryWhite} />
              </View>
              <Text style={styles.quickActionText}>Safety Check</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.quickAction}>
              <View style={[styles.quickActionIcon, { backgroundColor: colors.primaryShade }]}>
                <Ionicons name="people" size={24} color={colors.primaryWhite} />
              </View>
              <Text style={styles.quickActionText}>Find Support</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          
          <Card variant="bordered" style={styles.activityCard}>
            <View style={styles.activityItem}>
              <View style={styles.activityIcon}>
                <Ionicons name="document-text" size={20} color={colors.primary} />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>Safety guide completed</Text>
                <Text style={styles.activityTime}>2 hours ago</Text>
              </View>
            </View>
          </Card>

          <Card variant="bordered" style={styles.activityCard}>
            <View style={styles.activityItem}>
              <View style={styles.activityIcon}>
                <Ionicons name="checkmark-circle" size={20} color={colors.success} />
              </View>
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>Profile security updated</Text>
                <Text style={styles.activityTime}>1 day ago</Text>
              </View>
            </View>
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
  },
  greeting: {
    ...getTypography('title-b-20'),
    color: colors.primaryWhite,
  },
  subGreeting: {
    ...getTypography('paragraph-r-12'),
    color: colors.primaryLightShade,
    marginTop: spacing.xs,
  },
  notificationButton: {
    padding: spacing.sm,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: spacing.lg,
  },
  sectionTitle: {
    ...getTypography('title-b-16'),
    color: colors.textPrimary,
    marginBottom: spacing.md,
  },
  insightCard: {
    marginBottom: spacing.md,
  },
  insightHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  insightTitle: {
    ...getTypography('lead-sm-16'),
    color: colors.textPrimary,
  },
  insightScore: {
    ...getTypography('title-b-20'),
    color: colors.primary,
  },
  insightDescription: {
    ...getTypography('paragraph-r-12'),
    color: colors.textLight,
  },
  articleGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  articleCard: {
    flex: 1,
    marginHorizontal: spacing.xs,
  },
  articleImagePlaceholder: {
    height: 80,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    marginBottom: spacing.sm,
  },
  articleTitle: {
    ...getTypography('lead-sm-16'),
    color: colors.textPrimary,
    marginBottom: spacing.xs,
  },
  articleDescription: {
    ...getTypography('paragraph-r-10'),
    color: colors.textLight,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  quickAction: {
    alignItems: 'center',
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  quickActionText: {
    ...getTypography('paragraph-r-10'),
    color: colors.textPrimary,
    textAlign: 'center',
  },
  activityCard: {
    marginBottom: spacing.sm,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    ...getTypography('paragraph-m-12'),
    color: colors.textPrimary,
  },
  activityTime: {
    ...getTypography('paragraph-r-10'),
    color: colors.textLight,
    marginTop: spacing.xs,
  },
});
