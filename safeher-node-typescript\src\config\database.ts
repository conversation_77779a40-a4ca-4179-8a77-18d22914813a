import { Sequelize } from 'sequelize';
import dotenv from 'dotenv';

dotenv.config();

const isTest = process.env.NODE_ENV === 'test';

// Database configuration interface
interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  dialect: 'mysql';
}

const dbConfig: DatabaseConfig = {
  host: (isTest ? process.env.TEST_DB_HOST : process.env.DB_HOST) || 'localhost',
  port: parseInt((isTest ? process.env.TEST_DB_PORT : process.env.DB_PORT) || '3306'),
  database: (isTest ? process.env.TEST_DB_NAME : process.env.DB_NAME) || 'safeher_db',
  username: (isTest ? process.env.TEST_DB_USER : process.env.DB_USER) || 'root',
  password: (isTest ? process.env.TEST_DB_PASSWORD : process.env.DB_PASSWORD) || '',
  dialect: 'mysql' as const,
};

export const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  }
);

// Test database connection
export const testConnection = async (): Promise<boolean> => {
  try {
    await sequelize.authenticate();
    console.log('✅ Database connection has been established successfully.');
    return true;
  } catch (error) {
    console.error('❌ Unable to connect to the database:', error);
    return false;
  }
};

export default sequelize;
