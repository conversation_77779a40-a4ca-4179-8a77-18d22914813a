import jwt, { SignOptions, VerifyOptions } from 'jsonwebtoken';
import { JwtPayload } from '../types';

// Ensure JWT secrets are properly set
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '30d';

// Validate that secrets are not using default values in production
if (process.env.NODE_ENV === 'production') {
  if (JWT_SECRET === 'your-secret-key' || JWT_REFRESH_SECRET === 'your-refresh-secret-key') {
    throw new Error('JWT secrets must be set in production environment');
  }
}

export const generateAccessToken = (payload: JwtPayload): string => {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN as any,
    issuer: 'safeher-api',
    audience: 'safeher-client',
  });
};

export const generateRefreshToken = (payload: JwtPayload): string => {
  const options: SignOptions = {
    expiresIn: JWT_REFRESH_EXPIRES_IN as any,
    issuer: 'safeher-api',
    audience: 'safeher-client',
  };
  return jwt.sign(payload, JWT_REFRESH_SECRET, options);
};

export const verifyAccessToken = (token: string): JwtPayload => {
  try {
    const options: VerifyOptions = {
      issuer: 'safeher-api',
      audience: 'safeher-client',
    };
    const decoded = jwt.verify(token, JWT_SECRET, options) as JwtPayload;
    return decoded;
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
};

export const verifyRefreshToken = (token: string): JwtPayload => {
  try {
    const options: VerifyOptions = {
      issuer: 'safeher-api',
      audience: 'safeher-client',
    };
    const decoded = jwt.verify(token, JWT_REFRESH_SECRET, options) as JwtPayload;
    return decoded;
  } catch (error) {
    throw new Error('Invalid or expired refresh token');
  }
};

export const generateTokenPair = (payload: JwtPayload) => {
  const accessToken = generateAccessToken(payload);
  const refreshToken = generateRefreshToken(payload);
  
  return {
    accessToken,
    refreshToken,
    expiresIn: JWT_EXPIRES_IN,
  };
};

export const extractTokenFromHeader = (authHeader: string | undefined): string | null => {
  if (!authHeader) {
    return null;
  }

  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }

  return parts[1];
};
