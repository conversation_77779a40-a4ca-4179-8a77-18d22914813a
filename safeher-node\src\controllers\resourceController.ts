import { Response } from 'express';
import { Op } from 'sequelize';
import { Resource, User, Guardian } from '../models';
import { CustomError, asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest, ApiResponse, PaginatedResponse } from '../types';

export const createResource = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const {
    title,
    description,
    content,
    category,
    tags = [],
    attachmentUrls = [],
    isPublished = false,
  } = req.body;

  if (!req.user) {
    throw new CustomError('Authentication required', 401);
  }

  // Only guardians and admins can create resources
  if (req.user.role !== 'guardian' && req.user.role !== 'admin') {
    throw new CustomError('Only guardians and admins can create resources', 403);
  }

  const resource = await Resource.create({
    title,
    description,
    content,
    category,
    tags,
    published: isPublished,
    featured: false,
    author_id: req.user.id,
    views_count: 0,
    likes_count: 0,
  });

  // Fetch the created resource with author info
  const createdResource = await Resource.findByPk(resource.id, {
    include: [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'name', 'email'],
        include: [
          {
            model: Guardian,
            as: 'guardian',
            attributes: ['designation', 'organization'],
          },
        ],
      },
    ],
  });

  const response: ApiResponse = {
    success: true,
    message: 'Resource created successfully',
    data: {
      resource: {
        id: createdResource!.id,
        title: createdResource!.title,
        description: createdResource!.description,
        content: createdResource!.content,
        category: createdResource!.category,
        tags: createdResource!.tags,
        isPublished: createdResource!.published,
        featured: createdResource!.featured,
        viewsCount: createdResource!.views_count,
        likesCount: createdResource!.likes_count,
        downloadUrl: createdResource!.download_url,
        externalUrl: createdResource!.external_url,
        author: createdResource!.author,
        createdAt: createdResource!.created_at,
      },
    },
  };

  res.status(201).json(response);
});

export const getResources = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const {
    page = '1',
    limit = '10',
    search = '',
    category = '',
    tags = '',
    published = 'true',
    sort = 'created_at',
    order = 'DESC',
    my = 'false',
  } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  // Build where clause
  const whereClause: any = {};
  
  if (category && category !== 'all') {
    whereClause.category = category;
  }

  if (tags) {
    const tagArray = (tags as string).split(',').map(tag => tag.trim());
    whereClause.tags = {
      [Op.overlap]: tagArray,
    };
  }

  if (search) {
    whereClause[Op.or] = [
      { title: { [Op.like]: `%${search}%` } },
      { description: { [Op.like]: `%${search}%` } },
      { content: { [Op.like]: `%${search}%` } },
    ];
  }

  // Filter for user's own resources if requested
  if (my === 'true' && req.user) {
    whereClause.author_id = req.user.id;
  }

  // Only show published resources unless user is admin/guardian viewing their own
  if (published === 'true' && my !== 'true') {
    whereClause.is_published = true;
  } else if (req.user?.role !== 'admin' && req.user?.role !== 'guardian') {
    whereClause.is_published = true;
  }

  const { count, rows: resources } = await Resource.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'name', 'email'],
        include: [
          {
            model: Guardian,
            as: 'guardian',
            attributes: ['designation', 'organization'],
            required: false,
          },
        ],
      },
    ],
    limit: limitNum,
    offset,
    order: [[sort as string, order as string]],
  });

  const totalPages = Math.ceil(count / limitNum);

  const response: PaginatedResponse<any> = {
    data: resources.map(resource => ({
      id: resource.id,
      title: resource.title,
      description: resource.description,
      content: resource.content,
      category: resource.category,
      tags: resource.tags,
      isPublished: resource.published,
      featured: resource.featured,
      viewsCount: resource.views_count,
      likesCount: resource.likes_count,
      downloadUrl: resource.download_url,
      externalUrl: resource.external_url,
      author: resource.author,
      createdAt: resource.created_at,
      updatedAt: resource.updated_at,
    })),
    pagination: {
      page: pageNum,
      limit: limitNum,
      total: count,
      totalPages,
      hasNext: pageNum < totalPages,
      hasPrev: pageNum > 1,
    },
  };

  res.status(200).json({
    success: true,
    ...response,
  });
});

export const getResourceById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const resource = await Resource.findByPk(id, {
    include: [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'name', 'email'],
        include: [
          {
            model: Guardian,
            as: 'guardian',
            attributes: ['designation', 'organization'],
            required: false,
          },
        ],
      },
    ],
  });

  if (!resource) {
    throw new CustomError('Resource not found', 404);
  }

  // Check if user can view unpublished resource
  if (!resource.published) {
    if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'guardian' && resource.author_id !== req.user.id)) {
      throw new CustomError('Resource not found', 404);
    }
  }

  // Increment view count
  await resource.increment('views_count');

  const response: ApiResponse = {
    success: true,
    data: {
      resource: {
        id: resource.id,
        title: resource.title,
        description: resource.description,
        content: resource.content,
        category: resource.category,
        tags: resource.tags,
        isPublished: resource.published,
        featured: resource.featured,
        viewsCount: resource.views_count + 1, // Include the increment
        likesCount: resource.likes_count,
        downloadUrl: resource.download_url,
        externalUrl: resource.external_url,
        author: resource.author,
        createdAt: resource.created_at,
        updatedAt: resource.updated_at,
      },
    },
  };

  res.status(200).json(response);
});

export const updateResource = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const {
    title,
    description,
    content,
    category,
    tags,
    attachmentUrls,
    isPublished,
  } = req.body;

  const resource = await Resource.findByPk(id);
  if (!resource) {
    throw new CustomError('Resource not found', 404);
  }

  // Only author or admin can update resource
  if (req.user?.role !== 'admin' && resource.author_id !== req.user?.id) {
    throw new CustomError('Insufficient permissions', 403);
  }

  await resource.update({
    title: title || resource.title,
    description: description || resource.description,
    content: content || resource.content,
    category: category || resource.category,
    tags: tags || resource.tags,
    published: isPublished !== undefined ? isPublished : resource.published,
  });

  // Fetch updated resource
  const updatedResource = await Resource.findByPk(id, {
    include: [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'name', 'email'],
        include: [
          {
            model: Guardian,
            as: 'guardian',
            attributes: ['designation', 'organization'],
            required: false,
          },
        ],
      },
    ],
  });

  const response: ApiResponse = {
    success: true,
    message: 'Resource updated successfully',
    data: {
      resource: {
        id: updatedResource!.id,
        title: updatedResource!.title,
        description: updatedResource!.description,
        content: updatedResource!.content,
        category: updatedResource!.category,
        tags: updatedResource!.tags,
        isPublished: updatedResource!.published,
        featured: updatedResource!.featured,
        viewsCount: updatedResource!.views_count,
        likesCount: updatedResource!.likes_count,
        downloadUrl: updatedResource!.download_url,
        externalUrl: updatedResource!.external_url,
        author: updatedResource!.author,
        createdAt: updatedResource!.created_at,
        updatedAt: updatedResource!.updated_at,
      },
    },
  };

  res.status(200).json(response);
});

export const deleteResource = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const resource = await Resource.findByPk(id);
  if (!resource) {
    throw new CustomError('Resource not found', 404);
  }

  // Only author or admin can delete resource
  if (req.user?.role !== 'admin' && resource.author_id !== req.user?.id) {
    throw new CustomError('Insufficient permissions', 403);
  }

  await resource.destroy();

  const response: ApiResponse = {
    success: true,
    message: 'Resource deleted successfully',
  };

  res.status(200).json(response);
});

export const likeResource = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const resource = await Resource.findByPk(id);
  if (!resource) {
    throw new CustomError('Resource not found', 404);
  }

  if (!resource.published) {
    throw new CustomError('Resource not found', 404);
  }

  // Increment like count
  await resource.increment('likes_count');

  const response: ApiResponse = {
    success: true,
    message: 'Resource liked successfully',
    data: {
      likesCount: resource.likes_count + 1,
    },
  };

  res.status(200).json(response);
});
