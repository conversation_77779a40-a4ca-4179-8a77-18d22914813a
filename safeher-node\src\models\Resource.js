const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class Resource extends Model {
  // Associations
  static associate(models) {
    Resource.belongsTo(models.User, {
      foreignKey: 'author_id',
      as: 'author'
    });
  }
}

Resource.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    title: {
      type: DataTypes.STRING(500),
      allowNull: false,
      validate: {
        len: [5, 500],
        notEmpty: true,
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        len: [10, 1000],
        notEmpty: true,
      },
    },
    content: {
      type: DataTypes.TEXT('long'),
      allowNull: false,
      validate: {
        len: [50, 50000],
        notEmpty: true,
      },
    },
    category: {
      type: DataTypes.ENUM('guide', 'article', 'video', 'tool', 'legal', 'support'),
      allowNull: false,
      validate: {
        isIn: [['guide', 'article', 'video', 'tool', 'legal', 'support']],
      },
    },
    author_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    featured: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    published: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    views_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0,
      },
    },
    likes_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0,
      },
    },
    download_url: {
      type: DataTypes.STRING(1000),
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    external_url: {
      type: DataTypes.STRING(1000),
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'Resource',
    tableName: 'resources',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['author_id'],
      },
      {
        fields: ['category'],
      },
      {
        fields: ['featured'],
      },
      {
        fields: ['published'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['views_count'],
      },
      {
        fields: ['likes_count'],
      },
    ],
    hooks: {
      afterCreate: async (resource) => {
        // Increment guardian's resources count
        const { Guardian } = sequelize.models;
        await Guardian.increment('resources_count', {
          where: { user_id: resource.author_id }
        });
      },
      afterDestroy: async (resource) => {
        // Decrement guardian's resources count
        const { Guardian } = sequelize.models;
        await Guardian.decrement('resources_count', {
          where: { user_id: resource.author_id }
        });
      },
    },
  }
);

module.exports = Resource;
