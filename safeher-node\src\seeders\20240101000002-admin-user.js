'use strict';

const bcrypt = require('bcryptjs');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get admin role ID
    const [adminRole] = await queryInterface.sequelize.query(
      "SELECT id FROM roles WHERE name = 'admin'",
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (!adminRole) {
      throw new Error('Admin role not found. Please run roles seeder first.');
    }

    // Hash the default admin password
    const hashedPassword = await bcrypt.hash('SafeHer@Admin123!', 12);

    const adminUser = {
      name: 'SafeHer Admin',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      role_id: adminRole.id,
      is_active: true,
      email_verified: true,
      created_at: new Date(),
      updated_at: new Date()
    };

    await queryInterface.bulkInsert('users', [adminUser], {});

    console.log('✅ Admin user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: SafeHer@Admin123!');
    console.log('⚠️  Please change the password after first login!');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('users', {
      email: '<EMAIL>'
    }, {});
  }
};
