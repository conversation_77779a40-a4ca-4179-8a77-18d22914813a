import React from 'react';
import { View, ViewStyle, TouchableOpacity } from 'react-native';
import { createCardStyle } from '../../lib/design-tokens';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'bordered';
  style?: ViewStyle;
  onPress?: () => void;
  disabled?: boolean;
}

export default function Card({
  children,
  variant = 'default',
  style,
  onPress,
  disabled = false,
}: CardProps) {
  const cardStyle = createCardStyle(variant);

  if (onPress) {
    return (
      <TouchableOpacity
        style={[cardStyle, style]}
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.8}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={[cardStyle, style]}>
      {children}
    </View>
  );
}
