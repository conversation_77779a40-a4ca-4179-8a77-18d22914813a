const { z } = require('zod');

const createIncidentSchema = z.object({
  body: z.object({
    title: z
      .string()
      .min(5, 'Title must be at least 5 characters')
      .max(500, 'Title must be less than 500 characters')
      .trim(),
    description: z
      .string()
      .min(10, 'Description must be at least 10 characters')
      .max(5000, 'Description must be less than 5000 characters')
      .trim(),
    platform: z
      .enum(['facebook', 'instagram', 'twitter', 'tiktok', 'whatsapp', 'telegram', 'other'])
      .refine((val) => val, { message: 'Platform is required' }),
    category: z
      .enum(['body-shaming', 'harassment', 'cyberbullying', 'stalking', 'threats', 'doxxing', 'other'])
      .refine((val) => val, { message: 'Category is required' }),
    severity: z
      .enum(['low', 'medium', 'high', 'critical'])
      .default('medium')
      .optional(),
    url: z
      .string()
      .url('Invalid URL format')
      .optional()
      .or(z.literal('')),
    evidenceUrls: z
      .array(z.string().url('Invalid URL format'))
      .max(10, 'Maximum 10 evidence URLs allowed')
      .optional(),
    isAnonymous: z
      .boolean()
      .default(false)
      .optional(),
    reporterContact: z
      .string()
      .email('Invalid email format')
      .optional(),
    location: z
      .string()
      .max(255, 'Location must be less than 255 characters')
      .trim()
      .optional(),
  }).refine((data) => {
    // If anonymous, reporter contact is required
    if (data.isAnonymous && !data.reporterContact) {
      return false;
    }
    return true;
  }, {
    message: 'Reporter contact is required for anonymous reports',
    path: ['reporterContact'],
  }),
});

const updateIncidentStatusSchema = z.object({
  body: z.object({
    status: z
      .enum(['open', 'in_review', 'resolved', 'closed'])
      .refine((val) => val, { message: 'Status is required' }),
    notes: z
      .string()
      .max(1000, 'Notes must be less than 1000 characters')
      .trim()
      .optional(),
  }),
});

const assignGuardianSchema = z.object({
  body: z.object({
    guardianId: z
      .string()
      .uuid('Invalid guardian ID format')
      .refine((val) => val, { message: 'Guardian ID is required' }),
  }),
});

module.exports = {
  createIncidentSchema,
  updateIncidentStatusSchema,
  assignGuardianSchema,
};
