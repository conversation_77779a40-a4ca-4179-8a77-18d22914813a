const { User, Role, Guardian } = require('../models');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');

const getUserProfile = asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.user.id, {
    include: [
      {
        model: Role,
        as: 'role',
        attributes: ['id', 'name']
      },
      {
        model: Guardian,
        as: 'guardian',
        attributes: ['id', 'designation', 'organization', 'status', 'resources_count', 'reports_count'],
        required: false
      }
    ]
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  const response = {
    success: true,
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        role: user.role?.name || 'user',
        isActive: user.is_active,
        emailVerified: user.email_verified,
        lastLogin: user.last_login,
        guardian: user.guardian,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }
    }
  };

  res.status(200).json(response);
});

const updateUserProfile = asyncHandler(async (req, res) => {
  const { name, avatar } = req.body;

  const user = await User.findByPk(req.user.id);

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  const updateData = {};
  if (name !== undefined) updateData.name = name;
  if (avatar !== undefined) updateData.avatar = avatar;

  await user.update(updateData);

  const response = {
    success: true,
    message: 'Profile updated successfully',
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        updatedAt: user.updated_at
      }
    }
  };

  res.status(200).json(response);
});

const changePassword = asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  const user = await User.findByPk(req.user.id);

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  // Verify current password
  const isValidPassword = await user.validatePassword(currentPassword);
  if (!isValidPassword) {
    throw new CustomError('Current password is incorrect', 400);
  }

  // Update password
  await user.update({ password_hash: newPassword });

  const response = {
    success: true,
    message: 'Password changed successfully'
  };

  res.status(200).json(response);
});

const deactivateAccount = asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.user.id);

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  await user.update({ is_active: false });

  const response = {
    success: true,
    message: 'Account deactivated successfully'
  };

  res.status(200).json(response);
});

// Admin only endpoints
const getAllUsers = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    role,
    isActive,
    search
  } = req.query;

  // Only admins can access this endpoint
  if (req.user?.role !== 'admin') {
    throw new CustomError('Admin access required', 403);
  }

  const offset = (parseInt(page) - 1) * parseInt(limit);
  const whereClause = {};

  // Apply filters
  if (isActive !== undefined) whereClause.is_active = isActive === 'true';

  // Search functionality
  if (search) {
    const { Op } = require('sequelize');
    whereClause[Op.or] = [
      { name: { [Op.like]: `%${search}%` } },
      { email: { [Op.like]: `%${search}%` } }
    ];
  }

  const includeClause = [
    {
      model: Role,
      as: 'role',
      attributes: ['id', 'name'],
      where: role ? { name: role } : undefined
    },
    {
      model: Guardian,
      as: 'guardian',
      attributes: ['id', 'designation', 'organization', 'status'],
      required: false
    }
  ];

  const { rows: users, count } = await User.findAndCountAll({
    where: whereClause,
    include: includeClause,
    limit: parseInt(limit),
    offset,
    order: [['created_at', 'DESC']]
  });

  const response = {
    success: true,
    data: {
      users: users.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        role: user.role?.name || 'user',
        isActive: user.is_active,
        emailVerified: user.email_verified,
        lastLogin: user.last_login,
        guardian: user.guardian,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      })),
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(count / parseInt(limit))
      }
    }
  };

  res.status(200).json(response);
});

const getUserById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Only admins can access other users' details
  if (req.user?.role !== 'admin' && req.user?.id !== id) {
    throw new CustomError('Access denied', 403);
  }

  const user = await User.findByPk(id, {
    include: [
      {
        model: Role,
        as: 'role',
        attributes: ['id', 'name']
      },
      {
        model: Guardian,
        as: 'guardian',
        attributes: ['id', 'designation', 'organization', 'status', 'resources_count', 'reports_count'],
        required: false
      }
    ]
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  const response = {
    success: true,
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        role: user.role?.name || 'user',
        isActive: user.is_active,
        emailVerified: user.email_verified,
        lastLogin: user.last_login,
        guardian: user.guardian,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }
    }
  };

  res.status(200).json(response);
});

const updateUserStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { isActive } = req.body;

  // Only admins can update user status
  if (req.user?.role !== 'admin') {
    throw new CustomError('Admin access required', 403);
  }

  const user = await User.findByPk(id);

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  await user.update({ is_active: isActive });

  const response = {
    success: true,
    message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
    data: {
      user: {
        id: user.id,
        isActive: user.is_active,
        updatedAt: user.updated_at
      }
    }
  };

  res.status(200).json(response);
});

module.exports = {
  getUserProfile,
  updateUserProfile,
  changePassword,
  deactivateAccount,
  getAllUsers,
  getUserById,
  updateUserStatus
};
