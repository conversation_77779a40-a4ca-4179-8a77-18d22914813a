import request from 'supertest';
import app from '../index';
import { TestDataFactory, expectSuccess, expectValidationError, expectUnauthorized, expectForbidden } from './helpers/testUtils';

describe('Incidents Endpoints', () => {
  beforeEach(async () => {
    await TestDataFactory.createRoles();
  });

  describe('POST /api/incidents', () => {
    it('should create incident successfully as authenticated user', async () => {
      const user = await TestDataFactory.createUser();
      
      const incidentData = {
        title: 'Test Harassment Incident',
        description: 'Detailed description of the harassment incident',
        platform: 'instagram',
        category: 'harassment',
        severity: 'medium',
        url: 'https://instagram.com/example-post',
        evidenceUrls: ['https://example.com/evidence1.jpg'],
        location: 'Online Platform',
      };

      const response = await request(app)
        .post('/api/incidents')
        .set('Authorization', `Bearer ${user.token}`)
        .send(incidentData);

      expectSuccess(response, 201);
      expect(response.body.data.incident.title).toBe(incidentData.title);
      expect(response.body.data.incident.reporter_id).toBe(user.id);
      expect(response.body.data.incident.is_anonymous).toBe(false);
      expect(response.body.data.incident.status).toBe('open');
    });

    it('should create anonymous incident successfully', async () => {
      const incidentData = {
        title: 'Anonymous Harassment Report',
        description: 'Detailed description of the harassment incident',
        platform: 'twitter',
        category: 'cyberbullying',
        severity: 'high',
        isAnonymous: true,
        reporterContact: '<EMAIL>',
        location: 'Social Media',
      };

      const response = await request(app)
        .post('/api/incidents')
        .send(incidentData);

      expectSuccess(response, 201);
      expect(response.body.data.incident.title).toBe(incidentData.title);
      expect(response.body.data.incident.is_anonymous).toBe(true);
      expect(response.body.data.incident.reporter_contact).toBe(incidentData.reporterContact);
      expect(response.body.data.incident.reporter_id).toBeNull();
    });

    it('should fail with invalid platform', async () => {
      const user = await TestDataFactory.createUser();
      
      const incidentData = {
        title: 'Test Incident',
        description: 'Test description',
        platform: 'invalid_platform',
        category: 'harassment',
      };

      const response = await request(app)
        .post('/api/incidents')
        .set('Authorization', `Bearer ${user.token}`)
        .send(incidentData);

      expectValidationError(response, 'platform');
    });

    it('should fail with missing required fields', async () => {
      const user = await TestDataFactory.createUser();
      
      const incidentData = {
        title: 'Test Incident',
        // Missing description, platform, category
      };

      const response = await request(app)
        .post('/api/incidents')
        .set('Authorization', `Bearer ${user.token}`)
        .send(incidentData);

      expectValidationError(response);
    });
  });

  describe('GET /api/incidents', () => {
    it('should get incidents for authenticated user', async () => {
      const user = await TestDataFactory.createUser();
      const guardian = await TestDataFactory.createGuardian();
      
      // Create some incidents
      await TestDataFactory.createIncident(user.id);
      await TestDataFactory.createIncident(user.id, guardian.user.id);

      const response = await request(app)
        .get('/api/incidents')
        .set('Authorization', `Bearer ${user.token}`);

      expectSuccess(response);
      expect(response.body.data.incidents).toHaveLength(2);
      expect(response.body.data.pagination).toHaveProperty('total');
      expect(response.body.data.pagination).toHaveProperty('page');
      expect(response.body.data.pagination).toHaveProperty('limit');
    });

    it('should filter incidents by status', async () => {
      const user = await TestDataFactory.createUser();
      
      // Create incidents with different statuses
      const incident1 = await TestDataFactory.createIncident(user.id);
      const incident2 = await TestDataFactory.createIncident(user.id);
      
      // Update one incident status
      const { Incident } = require('../models');
      await Incident.update(
        { status: 'resolved' },
        { where: { id: incident2.id } }
      );

      const response = await request(app)
        .get('/api/incidents?status=open')
        .set('Authorization', `Bearer ${user.token}`);

      expectSuccess(response);
      expect(response.body.data.incidents).toHaveLength(1);
      expect(response.body.data.incidents[0].status).toBe('open');
    });

    it('should get all incidents for admin', async () => {
      const admin = await TestDataFactory.createUser({ role: 'admin' });
      const user1 = await TestDataFactory.createUser();
      const user2 = await TestDataFactory.createUser();
      
      // Create incidents for different users
      await TestDataFactory.createIncident(user1.id);
      await TestDataFactory.createIncident(user2.id);
      await TestDataFactory.createIncident(); // Anonymous

      const response = await request(app)
        .get('/api/incidents')
        .set('Authorization', `Bearer ${admin.token}`);

      expectSuccess(response);
      expect(response.body.data.incidents).toHaveLength(3);
    });

    it('should fail without authentication for non-anonymous incidents', async () => {
      const response = await request(app)
        .get('/api/incidents');

      expectUnauthorized(response);
    });
  });

  describe('GET /api/incidents/:id', () => {
    it('should get incident by ID for owner', async () => {
      const user = await TestDataFactory.createUser();
      const incident = await TestDataFactory.createIncident(user.id);

      const response = await request(app)
        .get(`/api/incidents/${incident.id}`)
        .set('Authorization', `Bearer ${user.token}`);

      expectSuccess(response);
      expect(response.body.data.incident.id).toBe(incident.id);
      expect(response.body.data.incident.title).toBe(incident.title);
    });

    it('should get incident by ID for assigned guardian', async () => {
      const user = await TestDataFactory.createUser();
      const guardian = await TestDataFactory.createGuardian();
      const incident = await TestDataFactory.createIncident(user.id, guardian.user.id);

      const response = await request(app)
        .get(`/api/incidents/${incident.id}`)
        .set('Authorization', `Bearer ${guardian.user.token}`);

      expectSuccess(response);
      expect(response.body.data.incident.id).toBe(incident.id);
    });

    it('should fail to access other user\'s incident', async () => {
      const user1 = await TestDataFactory.createUser();
      const user2 = await TestDataFactory.createUser();
      const incident = await TestDataFactory.createIncident(user1.id);

      const response = await request(app)
        .get(`/api/incidents/${incident.id}`)
        .set('Authorization', `Bearer ${user2.token}`);

      expectForbidden(response);
    });

    it('should get any incident for admin', async () => {
      const admin = await TestDataFactory.createUser({ role: 'admin' });
      const user = await TestDataFactory.createUser();
      const incident = await TestDataFactory.createIncident(user.id);

      const response = await request(app)
        .get(`/api/incidents/${incident.id}`)
        .set('Authorization', `Bearer ${admin.token}`);

      expectSuccess(response);
      expect(response.body.data.incident.id).toBe(incident.id);
    });
  });

  describe('PUT /api/incidents/:id/status', () => {
    it('should update incident status as guardian', async () => {
      const user = await TestDataFactory.createUser();
      const guardian = await TestDataFactory.createGuardian();
      const incident = await TestDataFactory.createIncident(user.id, guardian.user.id);

      const updateData = {
        status: 'in_progress',
        notes: 'Starting investigation',
      };

      const response = await request(app)
        .put(`/api/incidents/${incident.id}/status`)
        .set('Authorization', `Bearer ${guardian.user.token}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.data.incident.status).toBe('in_progress');
    });

    it('should fail to update status as regular user', async () => {
      const user1 = await TestDataFactory.createUser();
      const user2 = await TestDataFactory.createUser();
      const incident = await TestDataFactory.createIncident(user1.id);

      const updateData = {
        status: 'resolved',
      };

      const response = await request(app)
        .put(`/api/incidents/${incident.id}/status`)
        .set('Authorization', `Bearer ${user2.token}`)
        .send(updateData);

      expectForbidden(response);
    });

    it('should update status as admin', async () => {
      const admin = await TestDataFactory.createUser({ role: 'admin' });
      const user = await TestDataFactory.createUser();
      const incident = await TestDataFactory.createIncident(user.id);

      const updateData = {
        status: 'resolved',
        notes: 'Case resolved by admin',
      };

      const response = await request(app)
        .put(`/api/incidents/${incident.id}/status`)
        .set('Authorization', `Bearer ${admin.token}`)
        .send(updateData);

      expectSuccess(response);
      expect(response.body.data.incident.status).toBe('resolved');
    });
  });
});
