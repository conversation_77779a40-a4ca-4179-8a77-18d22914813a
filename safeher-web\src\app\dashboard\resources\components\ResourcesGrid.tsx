'use client';

import { useState } from 'react';
import { getTypography, createCardVariant } from '@/lib/design-tokens';
import { ResourcePreviewModal } from './ResourcePreviewModal';

// Mock data for resources
const mockResources = [
  {
    id: 1,
    title: 'Online Gender-Based Violence: Breaking Down the Digital Threats',
    category: 'Cybersecurity',
    date: '3rd October 2024',
    excerpt: 'The internet has transformed the way we communicate, connect, and conduct our daily lives. While this digital revolution has also given rise to a troubling phenomenon: Online Gender-Based Violence (OGBV). This pervasive issue poses a significant challenge to gender equality and women\'s empowerment.',
    readTime: '5 min read',
    author: 'SafeHer Team',
  },
  {
    id: 2,
    title: 'Unmasking the Threat: Tackling Cyber Bullying Head-On',
    category: 'Digital Safety',
    date: '1st October 2024',
    excerpt: 'In today\'s digital age, our lives are increasingly intertwined with technology. From smartphones to computers, our digital interactions have become an integral part of our daily lives. However, this digital revolution has brought many benefits, it has also given rise to a darker side: cyberbullying.',
    readTime: '7 min read',
    author: 'Dr. <PERSON>',
  },
  {
    id: 3,
    title: 'Navigating the Digital Abyss: Ensuring Online Safety in a Connected World',
    category: 'Online Safety',
    date: '28th September 2024',
    excerpt: 'In today\'s hyper-connected world, the internet has become an integral part of our daily lives. It has revolutionized the way we communicate, work, learn, and entertain ourselves. However, this digital transformation has also given rise to numerous challenges, particularly in the realm of online safety and security.',
    readTime: '6 min read',
    author: 'Tech Safety Institute',
  },
  {
    id: 4,
    title: 'Cybersecurity Unleashed: Protecting the Digital World through Training',
    category: 'Training',
    date: '3rd October 2024',
    excerpt: 'In today\'s interconnected world, where cyberspace plays a pivotal role in our daily lives, the need for robust cybersecurity has never been more critical. This blog delves into the increasing importance of cyber security, including its definitions, importance, and the need for comprehensive training and awareness.',
    readTime: '8 min read',
    author: 'CyberSec Academy',
  },
  {
    id: 5,
    title: 'The Complex Landscape of Online Hate Speech: Challenges and Responses',
    category: 'Policy',
    date: '1st October 2024',
    excerpt: 'Online hate speech has emerged as a pervasive issue with profound implications for individuals, communities, and society as a whole. This complex phenomenon defies easy definition, as it intersects with a web of legal, ethical, and societal considerations. In this blog post, we will explore the multifaceted nature of online hate speech.',
    readTime: '9 min read',
    author: 'Digital Rights Foundation',
  },
  {
    id: 6,
    title: 'Shattering Digital Barriers: Women\'s Rights in the Modern World',
    category: 'Women\'s Rights',
    date: '25th September 2024',
    excerpt: 'Digital inequality has emerged as both a consequence and a driver of broader societal disparities. The digital divide encompasses not only access to technology and meaningful participation in the digital sphere are pivotal components of women\'s rights in the online realm, exploring how digital transformation can be leveraged to advance women\'s rights in the modern world.',
    readTime: '10 min read',
    author: 'Women\'s Digital Rights Coalition',
  },
];

export function ResourcesGrid() {
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedResource, setSelectedResource] = useState<typeof mockResources[0] | null>(null);
  const totalPages = 10;

  return (
    <>
      <div className="space-y-6">
        {/* Resources Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockResources.map((resource) => (
            <ResourceCard 
              key={resource.id} 
              resource={resource} 
              onPreview={() => setSelectedResource(resource)}
            />
          ))}
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className={`px-4 py-2 text-sm font-medium text-text-light border border-border-light rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            Previous
          </button>
          
          <div className="flex items-center space-x-2">
            {[1, 2, 3, '...', 8, 9, 10].map((page, index) => (
              <button
                key={index}
                onClick={() => typeof page === 'number' && setCurrentPage(page)}
                className={`px-3 py-2 text-sm font-medium rounded-lg ${
                  page === currentPage
                    ? 'bg-primary text-white'
                    : 'text-text-light hover:bg-gray-50'
                } ${typeof page !== 'number' ? 'cursor-default' : ''}`}
                disabled={typeof page !== 'number'}
              >
                {page}
              </button>
            ))}
          </div>

          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className={`px-4 py-2 text-sm font-medium text-text-light border border-border-light rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            Next
          </button>
        </div>
      </div>

      <ResourcePreviewModal 
        resource={selectedResource}
        isOpen={!!selectedResource}
        onClose={() => setSelectedResource(null)}
      />
    </>
  );
}

function ResourceCard({ 
  resource, 
  onPreview 
}: { 
  resource: typeof mockResources[0];
  onPreview: () => void;
}) {
  return (
    <div className={`${createCardVariant('bordered')} p-6 space-y-4 hover:shadow-lg transition-shadow`}>
      {/* Category and Date */}
      <div className="flex items-center justify-between">
        <span className={`px-3 py-1 bg-primary/10 text-primary rounded-full ${getTypography('paragraph-r-10')}`}>
          {resource.category}
        </span>
        <span className={`${getTypography('paragraph-r-10')} text-text-light`}>
          {resource.date}
        </span>
      </div>

      {/* Title */}
      <h3 className={`${getTypography('title-b-16')} text-text-primary line-clamp-2`}>
        {resource.title}
      </h3>

      {/* Excerpt */}
      <p className={`${getTypography('paragraph-r-12')} text-text-dark line-clamp-3`}>
        {resource.excerpt}
      </p>

      {/* Footer */}
      <div className="flex items-center justify-between pt-4 border-t border-border-light">
        <div className="flex items-center space-x-4">
          <span className={`${getTypography('paragraph-r-10')} text-text-light`}>
            {resource.readTime}
          </span>
          <span className={`${getTypography('paragraph-r-10')} text-text-light`}>
            by {resource.author}
          </span>
        </div>
        <button
          onClick={onPreview}
          className={`${getTypography('link-r-12')} text-primary hover:text-primary-shade`}
        >
          Read story →
        </button>
      </div>
    </div>
  );
}
