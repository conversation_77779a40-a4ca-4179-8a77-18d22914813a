const express = require('express');
const {
  get<PERSON><PERSON><PERSON>,
  get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  approve<PERSON><PERSON><PERSON>,
  reject<PERSON><PERSON><PERSON>,
  suspend<PERSON><PERSON><PERSON>,
  updateGuardianProfile
} = require('../controllers/guardianController');
const { authenticate, requireAdmin, optionalAuth } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * /api/guardians:
 *   get:
 *     summary: Get guardians list
 *     tags: [Guardians]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, approved, rejected, suspended]
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Guardians retrieved successfully
 */
router.get('/', optionalAuth, getGuardians);

/**
 * @swagger
 * /api/guardians/{id}:
 *   get:
 *     summary: Get guardian by ID
 *     tags: [Guardians]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Guardian retrieved successfully
 *       404:
 *         description: Guardian not found
 */
router.get('/:id', optionalAuth, getGuardianById);

/**
 * @swagger
 * /api/guardians/{id}/approve:
 *   put:
 *     summary: Approve guardian application
 *     tags: [Guardians]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Guardian approved successfully
 *       400:
 *         description: Guardian is not in pending status
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Guardian not found
 */
router.put('/:id/approve', authenticate, requireAdmin, approveGuardian);

/**
 * @swagger
 * /api/guardians/{id}/reject:
 *   put:
 *     summary: Reject guardian application
 *     tags: [Guardians]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Guardian application rejected
 *       400:
 *         description: Guardian is not in pending status
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Guardian not found
 */
router.put('/:id/reject', authenticate, requireAdmin, rejectGuardian);

/**
 * @swagger
 * /api/guardians/{id}/suspend:
 *   put:
 *     summary: Suspend guardian
 *     tags: [Guardians]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Guardian suspended successfully
 *       400:
 *         description: Guardian is not approved
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *       404:
 *         description: Guardian not found
 */
router.put('/:id/suspend', authenticate, requireAdmin, suspendGuardian);

/**
 * @swagger
 * /api/guardians/{id}/profile:
 *   put:
 *     summary: Update guardian profile
 *     tags: [Guardians]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               designation:
 *                 type: string
 *               organization:
 *                 type: string
 *               bio:
 *                 type: string
 *               expertiseAreas:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Guardian profile updated successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Guardian not found
 */
router.put('/:id/profile', authenticate, updateGuardianProfile);

module.exports = router;
