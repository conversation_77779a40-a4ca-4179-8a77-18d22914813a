import WebsiteLayout from '@/components/global/layout/WebsiteLayout';

export default function AboutPage() {
  return (
    <WebsiteLayout>
      <div className="bg-white py-16 lg:py-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              About SafeHer
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Empowering women to navigate the digital world safely and confidently
            </p>
          </div>

          <div className="space-y-12">
            <section>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Mission</h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                SafeHer by SafeOnline Women Kenya is dedicated to combating online violence against women. 
                We believe every woman deserves to feel safe and empowered in digital spaces. Our platform 
                provides tools, resources, and community support to help women identify, report, and overcome 
                online harassment and abuse.
              </p>
            </section>

            <section>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">What We Do</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="bg-blue-50 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Community Reporting</h3>
                  <p className="text-gray-600">
                    Enable women to report incidents of online violence and harassment, 
                    creating a comprehensive database to track and address these issues.
                  </p>
                </div>
                <div className="bg-purple-50 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Educational Resources</h3>
                  <p className="text-gray-600">
                    Provide guides, articles, and tools to help women understand and 
                    protect themselves from various forms of online violence.
                  </p>
                </div>
                <div className="bg-green-50 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Guardian Network</h3>
                  <p className="text-gray-600">
                    Connect women with trained guardians and volunteers who can provide 
                    support, guidance, and assistance when needed.
                  </p>
                </div>
                <div className="bg-pink-50 rounded-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Safe Spaces</h3>
                  <p className="text-gray-600">
                    Create supportive online communities where women can share experiences, 
                    seek advice, and find solidarity with others.
                  </p>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Impact</h2>
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-8 text-white">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                  <div>
                    <div className="text-4xl font-bold mb-2">200K+</div>
                    <div className="text-blue-100">Incidents Reported</div>
                  </div>
                  <div>
                    <div className="text-4xl font-bold mb-2">2M+</div>
                    <div className="text-blue-100">Women Supported</div>
                  </div>
                  <div>
                    <div className="text-4xl font-bold mb-2">40K+</div>
                    <div className="text-blue-100">Active Guardians</div>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </WebsiteLayout>
  );
}
