import { Router } from 'express';
import {
  createIncident,
  getIncidents,
  getIncidentById,
  updateIncidentStatus,
  deleteIncident,
} from '../controllers/incidentController';
import { authenticate, optionalAuth, requireG<PERSON>ian<PERSON>rAdmin } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { z } from 'zod';

const router = Router();

// Validation schemas
const createIncidentSchema = z.object({
  body: z.object({
    title: z.string().min(5).max(255).trim(),
    description: z.string().min(10).max(2000).trim(),
    platform: z.enum(['facebook', 'instagram', 'twitter', 'tiktok', 'youtube', 'whatsapp', 'telegram', 'other']),
    category: z.enum(['harassment', 'cyberbullying', 'stalking', 'doxxing', 'revenge_porn', 'hate_speech', 'other']),
    severity: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
    url: z.string().url().optional(),
    evidenceUrls: z.array(z.string().url()).max(10).default([]),
    isAnonymous: z.boolean().default(false),
    reporterContact: z.string().email().optional(),
    location: z.string().max(255).trim().optional(),
  }).refine((data) => {
    if (data.isAnonymous && !data.reporterContact) {
      return false;
    }
    return true;
  }, {
    message: "Reporter contact is required for anonymous reports",
    path: ["reporterContact"],
  }),
});

const updateIncidentStatusSchema = z.object({
  body: z.object({
    status: z.enum(['open', 'in_progress', 'resolved', 'closed']),
    guardianId: z.string().uuid().optional(),
  }),
});

/**
 * @swagger
 * /api/incidents:
 *   post:
 *     summary: Create a new incident report
 *     tags: [Incidents]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *               - platform
 *               - category
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 5
 *                 maxLength: 255
 *               description:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 2000
 *               platform:
 *                 type: string
 *                 enum: [facebook, instagram, twitter, tiktok, youtube, whatsapp, telegram, other]
 *               category:
 *                 type: string
 *                 enum: [harassment, cyberbullying, stalking, doxxing, revenge_porn, hate_speech, other]
 *               severity:
 *                 type: string
 *                 enum: [low, medium, high, critical]
 *                 default: medium
 *               url:
 *                 type: string
 *                 format: uri
 *               evidenceUrls:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uri
 *                 maxItems: 10
 *               isAnonymous:
 *                 type: boolean
 *                 default: false
 *               reporterContact:
 *                 type: string
 *                 format: email
 *               location:
 *                 type: string
 *                 maxLength: 255
 *     responses:
 *       201:
 *         description: Incident reported successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized (for non-anonymous reports)
 */
router.post('/', optionalAuth, validate(createIncidentSchema), createIncident);

/**
 * @swagger
 * /api/incidents:
 *   get:
 *     summary: Get paginated list of incidents
 *     tags: [Incidents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [open, in_progress, resolved, closed]
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *       - in: query
 *         name: platform
 *         schema:
 *           type: string
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [low, medium, high, critical]
 *       - in: query
 *         name: my
 *         schema:
 *           type: boolean
 *           default: false
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           default: created_at
 *       - in: query
 *         name: order
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: DESC
 *     responses:
 *       200:
 *         description: Incidents retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/', optionalAuth, getIncidents);

/**
 * @swagger
 * /api/incidents/{id}:
 *   get:
 *     summary: Get incident by ID
 *     tags: [Incidents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Incident retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Incident not found
 */
router.get('/:id', optionalAuth, getIncidentById);

/**
 * @swagger
 * /api/incidents/{id}/status:
 *   patch:
 *     summary: Update incident status (Guardian/Admin only)
 *     tags: [Incidents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [open, in_progress, resolved, closed]
 *               guardianId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Incident status updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Incident not found
 */
router.patch('/:id/status', authenticate, requireGuardianOrAdmin, validate(updateIncidentStatusSchema), updateIncidentStatus);

/**
 * @swagger
 * /api/incidents/{id}:
 *   delete:
 *     summary: Delete incident (Admin or reporter only)
 *     tags: [Incidents]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Incident deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Incident not found
 */
router.delete('/:id', authenticate, deleteIncident);

export default router;
