import { sequelize } from '../config/database';
import Role from './Role';
import User from './User';
import Guardian from './Guardian';
import Incident from './Incident';
import Report from './Report';
import Resource from './Resource';

// Define all models
const models = {
  Role,
  User,
  Guardian,
  Incident,
  Report,
  Resource,
};

// Set up associations
Object.values(models).forEach((model: any) => {
  if (model.associate) {
    model.associate(models);
  }
});

// Export models and sequelize instance
export {
  sequelize,
  Role,
  User,
  Guardian,
  Incident,
  Report,
  Resource,
};

export default models;
