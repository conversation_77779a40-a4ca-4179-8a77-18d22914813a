const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class Incident extends Model {
  // Associations
  static associate(models) {
    Incident.belongsTo(models.User, {
      foreignKey: 'reporter_id',
      as: 'reporter'
    });

    Incident.belongsTo(models.User, {
      foreignKey: 'guardian_id',
      as: 'guardian'
    });

    Incident.hasMany(models.Report, {
      foreignKey: 'incident_id',
      as: 'reports'
    });
  }
}

Incident.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    reporter_id: {
      type: DataTypes.UUID,
      allowNull: true, // Allow null for anonymous reports
      references: {
        model: 'users',
        key: 'id',
      },
    },
    guardian_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    title: {
      type: DataTypes.STRING(500),
      allowNull: false,
      validate: {
        len: [5, 500],
        notEmpty: true,
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        len: [10, 5000],
        notEmpty: true,
      },
    },
    platform: {
      type: DataTypes.ENUM('facebook', 'instagram', 'twitter', 'tiktok', 'whatsapp', 'telegram', 'other'),
      allowNull: false,
      validate: {
        isIn: [['facebook', 'instagram', 'twitter', 'tiktok', 'whatsapp', 'telegram', 'other']],
      },
    },
    category: {
      type: DataTypes.ENUM('body-shaming', 'harassment', 'cyberbullying', 'stalking', 'threats', 'doxxing', 'other'),
      allowNull: false,
      validate: {
        isIn: [['body-shaming', 'harassment', 'cyberbullying', 'stalking', 'threats', 'doxxing', 'other']],
      },
    },
    status: {
      type: DataTypes.ENUM('open', 'in_review', 'resolved', 'closed'),
      allowNull: false,
      defaultValue: 'open',
      validate: {
        isIn: [['open', 'in_review', 'resolved', 'closed']],
      },
    },
    severity: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
      allowNull: false,
      defaultValue: 'medium',
      validate: {
        isIn: [['low', 'medium', 'high', 'critical']],
      },
    },
    url: {
      type: DataTypes.STRING(1000),
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    evidence_urls: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    is_anonymous: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    reporter_contact: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        isEmail: true,
      },
    },
    location: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'Incident',
    tableName: 'incidents',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['reporter_id'],
      },
      {
        fields: ['guardian_id'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['category'],
      },
      {
        fields: ['platform'],
      },
      {
        fields: ['severity'],
      },
      {
        fields: ['is_anonymous'],
      },
      {
        fields: ['created_at'],
      },
    ],
    validate: {
      anonymousReportValidation() {
        if (this.is_anonymous && !this.reporter_contact) {
          throw new Error('Anonymous reports must include reporter contact information');
        }
        if (this.is_anonymous && this.reporter_id) {
          throw new Error('Anonymous reports cannot have a reporter_id');
        }
        if (!this.is_anonymous && !this.reporter_id) {
          throw new Error('Non-anonymous reports must have a reporter_id');
        }
      },
    },
  }
);

module.exports = Incident;
