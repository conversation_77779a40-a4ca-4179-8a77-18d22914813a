// SafeHer Design Tokens
// Typography utilities based on Figma design tokens

export const typography = {
  // Titles
  'title-bold-64': 'text-[64px] leading-[76.8px] font-bold font-source-sans',
  'title-b-20': 'text-[20px] leading-[24px] font-bold font-source-sans',
  'title-b-16': 'text-[18px] leading-[21.6px] font-bold font-source-sans',
  'title-b-14': 'text-[14px] leading-[16.8px] font-bold font-source-sans',
  
  // Leads
  'lead-r-27': 'text-[27px] leading-[37.8px] font-normal font-source-sans',
  'lead-sm-20': 'text-[20px] leading-[28px] font-semibold font-source-sans',
  'lead-sm-16': 'text-[16px] leading-[22.4px] font-semibold font-source-sans',
  'lead-r-14': 'text-[14px] leading-[19.6px] font-normal font-source-sans',
  'lead-b-12': 'text-[14px] leading-[19.6px] font-bold font-source-sans',
  'lead-sm-10': 'text-[10px] leading-[14px] font-semibold font-source-sans',
  'lead-r-10': 'text-[10px] leading-[14px] font-normal font-source-sans',
  
  // Paragraphs
  'paragraph-r-20': 'text-[20px] leading-[32px] font-normal font-nunito',
  'paragraph-m-16': 'text-[16px] leading-[25.6px] font-medium font-nunito',
  'paragraph-m-12': 'text-[12px] leading-[14.4px] font-medium font-nunito',
  'paragraph-r-12': 'text-[12px] leading-[14.4px] font-normal font-nunito',
  'paragraph-m-10': 'text-[12px] leading-[19.2px] font-medium font-nunito',
  'paragraph-r-10': 'text-[10px] leading-[12px] font-normal font-nunito',
  
  // Links
  'link-r-20': 'text-[20px] leading-[32px] font-normal font-montserrat',
  'link-r-16': 'text-[16px] leading-[25.6px] font-normal font-montserrat',
  'link-r-12': 'text-[12px] leading-[19.2px] font-normal font-montserrat',
  
  // Buttons
  'button-r-20': 'text-[20px] leading-[32px] font-bold font-montserrat',
} as const;

export const colors = {
  primary: {
    DEFAULT: '#cf1884',
    shade: '#f651ad',
    light: '#fe83c8',
    white: '#ffffff',
  },
  secondary: {
    DEFAULT: '#3364b3',
    shade: '#5f8edb',
    light: '#98b0d6',
  },
  text: {
    primary: '#2b1a55',
    dark: '#48396c',
    light: '#706390',
    lighter: '#c0b9d1',
  },
  border: {
    dark: '#d0d5dd',
    light: '#eaecf0',
  },
} as const;

export const shadows = {
  sm: '0 4px 10px rgba(208, 213, 221, 0.25)',
  presentation: '0 0 38px rgba(0, 0, 0, 0.17), 0 0 17px rgba(0, 0, 0, 0.17)',
} as const;

// Utility function to get typography classes
export function getTypography(variant: keyof typeof typography): string {
  return typography[variant];
}

// Utility function to create consistent button styles
export function createButtonVariant(
  variant: 'primary' | 'secondary' | 'outline' | 'ghost' = 'primary'
): string {
  const base = 'inline-flex items-center justify-center rounded-lg px-6 py-3 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  switch (variant) {
    case 'primary':
      return `${base} bg-primary text-white hover:bg-primary-shade focus:ring-primary`;
    case 'secondary':
      return `${base} bg-secondary text-white hover:bg-secondary-shade focus:ring-secondary`;
    case 'outline':
      return `${base} border-2 border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary`;
    case 'ghost':
      return `${base} text-primary hover:bg-primary/10 focus:ring-primary`;
    default:
      return `${base} bg-primary text-white hover:bg-primary-shade focus:ring-primary`;
  }
}

// Utility function to create consistent card styles
export function createCardVariant(
  variant: 'default' | 'elevated' | 'bordered' = 'default'
): string {
  const base = 'bg-white rounded-lg';
  
  switch (variant) {
    case 'elevated':
      return `${base} shadow-lg`;
    case 'bordered':
      return `${base} border border-border-light`;
    default:
      return `${base} shadow-sm`;
  }
}
