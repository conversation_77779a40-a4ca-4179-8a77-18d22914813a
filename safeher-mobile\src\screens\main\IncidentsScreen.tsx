import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import Card from '../../components/ui/Card';
import { colors, getTypography, spacing, borderRadius, createInputStyle } from '../../lib/design-tokens';

type TabType = 'Community' | 'New Reports' | 'My Reports';

export default function IncidentsScreen() {
  const [activeTab, setActiveTab] = useState<TabType>('Community');
  const [searchQuery, setSearchQuery] = useState('');

  const tabs: TabType[] = ['Community', 'New Reports', 'My Reports'];

  const renderIncidentCard = (incident: any, index: number) => (
    <Card key={index} variant="bordered" style={styles.incidentCard}>
      <View style={styles.incidentHeader}>
        <View style={styles.userInfo}>
          <View style={styles.avatar} />
          <View style={styles.userDetails}>
            <Text style={styles.userName}>Anonymous User</Text>
            <Text style={styles.timeStamp}>2 hours ago</Text>
          </View>
        </View>
        <TouchableOpacity style={styles.moreButton}>
          <Ionicons name="ellipsis-horizontal" size={20} color={colors.textLight} />
        </TouchableOpacity>
      </View>

      <Text style={styles.incidentContent}>
        Experienced harassment on social media platform. Looking for advice on how to handle this situation safely.
      </Text>

      {/* Incident Image Placeholder */}
      <View style={styles.incidentImage} />

      <View style={styles.incidentActions}>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="heart-outline" size={20} color={colors.textLight} />
          <Text style={styles.actionText}>Support</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="chatbubble-outline" size={20} color={colors.textLight} />
          <Text style={styles.actionText}>Comment</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="share-outline" size={20} color={colors.textLight} />
          <Text style={styles.actionText}>Share</Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Incidents</Text>
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add" size={24} color={colors.primaryWhite} />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color={colors.textLight} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search incidents..."
            placeholderTextColor={colors.textLighter}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          <TouchableOpacity style={styles.filterButton}>
            <Ionicons name="options" size={20} color={colors.textLight} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tabs */}
      <View style={styles.tabContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[
                styles.tab,
                activeTab === tab && styles.activeTab,
              ]}
              onPress={() => setActiveTab(tab)}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab && styles.activeTabText,
                ]}
              >
                {tab}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'Community' && (
          <View style={styles.incidentsList}>
            {[1, 2, 3, 4].map((_, index) => renderIncidentCard(_, index))}
          </View>
        )}

        {activeTab === 'New Reports' && (
          <View style={styles.emptyState}>
            <Ionicons name="document-text-outline" size={64} color={colors.textLighter} />
            <Text style={styles.emptyTitle}>No New Reports</Text>
            <Text style={styles.emptyDescription}>
              New incident reports will appear here
            </Text>
          </View>
        )}

        {activeTab === 'My Reports' && (
          <View style={styles.emptyState}>
            <Ionicons name="folder-outline" size={64} color={colors.textLighter} />
            <Text style={styles.emptyTitle}>No Reports Yet</Text>
            <Text style={styles.emptyDescription}>
              Your reported incidents will appear here
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Floating Action Button */}
      <TouchableOpacity style={styles.fab}>
        <Ionicons name="add" size={24} color={colors.primaryWhite} />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    paddingTop: spacing.lg,
  },
  headerTitle: {
    ...getTypography('title-b-20'),
    color: colors.primaryWhite,
  },
  addButton: {
    padding: spacing.sm,
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.primary,
  },
  searchBar: {
    ...createInputStyle(),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primaryWhite,
    borderColor: 'transparent',
  },
  searchIcon: {
    marginRight: spacing.sm,
  },
  searchInput: {
    flex: 1,
    ...getTypography('paragraph-m-16'),
    color: colors.textPrimary,
    paddingVertical: 0,
  },
  filterButton: {
    padding: spacing.sm,
  },
  tabContainer: {
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  tab: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    marginRight: spacing.sm,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    ...getTypography('paragraph-m-16'),
    color: colors.textLight,
  },
  activeTabText: {
    color: colors.primary,
  },
  content: {
    flex: 1,
  },
  incidentsList: {
    padding: spacing.lg,
  },
  incidentCard: {
    marginBottom: spacing.md,
  },
  incidentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    marginRight: spacing.sm,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    ...getTypography('paragraph-m-12'),
    color: colors.textPrimary,
  },
  timeStamp: {
    ...getTypography('paragraph-r-10'),
    color: colors.textLight,
    marginTop: spacing.xs,
  },
  moreButton: {
    padding: spacing.sm,
  },
  incidentContent: {
    ...getTypography('paragraph-r-12'),
    color: colors.textPrimary,
    marginBottom: spacing.md,
    lineHeight: 18,
  },
  incidentImage: {
    height: 120,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    marginBottom: spacing.md,
  },
  incidentActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.borderLight,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  actionText: {
    ...getTypography('paragraph-r-12'),
    color: colors.textLight,
    marginLeft: spacing.xs,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxxl,
  },
  emptyTitle: {
    ...getTypography('title-b-16'),
    color: colors.textPrimary,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptyDescription: {
    ...getTypography('paragraph-r-12'),
    color: colors.textLight,
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    bottom: spacing.lg,
    right: spacing.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});
