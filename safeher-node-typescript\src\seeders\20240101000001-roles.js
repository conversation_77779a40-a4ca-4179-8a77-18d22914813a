'use strict';

const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const roles = [
      {
        id: uuidv4(),
        name: 'admin',
        permissions: {
          users: ['create', 'read', 'update', 'delete'],
          guardians: ['create', 'read', 'update', 'delete', 'approve', 'reject', 'revoke'],
          incidents: ['create', 'read', 'update', 'delete', 'assign'],
          reports: ['create', 'read', 'update', 'delete'],
          resources: ['create', 'read', 'update', 'delete', 'publish'],
          dashboard: ['view_all_stats', 'export_data']
        },
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'guardian',
        permissions: {
          users: ['read'],
          guardians: ['read'],
          incidents: ['create', 'read', 'update', 'assign'],
          reports: ['create', 'read', 'update'],
          resources: ['create', 'read', 'update', 'delete'],
          dashboard: ['view_assigned_stats']
        },
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        name: 'user',
        permissions: {
          incidents: ['create', 'read_own'],
          reports: ['create', 'read_own'],
          resources: ['read'],
          profile: ['read', 'update']
        },
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('roles', roles, {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('roles', null, {});
  }
};
