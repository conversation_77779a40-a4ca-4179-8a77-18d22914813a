import { User, Role, Guardian } from '../../models';
import { generateTokenPair } from '../../utils/jwt';
import bcrypt from 'bcryptjs';

export interface TestUser {
  id: string;
  name: string;
  email: string;
  role: string;
  token: string;
}

export class TestDataFactory {
  static async createRoles() {
    const roles = [
      {
        name: 'admin',
        permissions: {
          users: ['create', 'read', 'update', 'delete'],
          guardians: ['create', 'read', 'update', 'delete', 'approve', 'reject', 'revoke'],
          incidents: ['create', 'read', 'update', 'delete', 'assign'],
          reports: ['create', 'read', 'update', 'delete'],
          resources: ['create', 'read', 'update', 'delete', 'publish'],
        },
      },
      {
        name: 'guardian',
        permissions: {
          incidents: ['create', 'read', 'update', 'assign'],
          reports: ['create', 'read', 'update'],
          resources: ['create', 'read', 'update', 'delete'],
        },
      },
      {
        name: 'user',
        permissions: {
          incidents: ['create', 'read_own'],
          reports: ['create', 'read_own'],
          resources: ['read'],
        },
      },
    ];

    const createdRoles = [];
    for (const roleData of roles) {
      const role = await Role.create(roleData);
      createdRoles.push(role);
    }

    return createdRoles;
  }

  static async createUser(
    userData: {
      name?: string;
      email?: string;
      password?: string;
      role?: string;
      isActive?: boolean;
      emailVerified?: boolean;
    } = {}
  ): Promise<TestUser> {
    const {
      name = 'Test User',
      email = `test${Date.now()}@example.com`,
      password = 'password123',
      role = 'user',
      isActive = true,
      emailVerified = true,
    } = userData;

    // Get role
    const userRole = await Role.findOne({ where: { name: role } });
    if (!userRole) {
      throw new Error(`Role ${role} not found`);
    }

    // Create user
    const user = await User.create({
      name,
      email,
      password_hash: password, // Will be hashed by model hook
      role_id: userRole.id,
      is_active: isActive,
      email_verified: emailVerified,
    });

    // Generate token
    const tokens = generateTokenPair({
      id: user.id,
      email: user.email,
      role: role,
      roleId: userRole.id,
    });

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      role: role,
      token: tokens.accessToken,
    };
  }

  static async createGuardian(userData: {
    name?: string;
    email?: string;
    password?: string;
    designation?: string;
    organization?: string;
    status?: 'pending' | 'approved' | 'rejected' | 'suspended';
  } = {}): Promise<{ user: TestUser; guardian: any }> {
    const {
      name = 'Test Guardian',
      email = `guardian${Date.now()}@example.com`,
      password = 'password123',
      designation = 'Test Specialist',
      organization = 'Test Organization',
      status = 'approved',
    } = userData;

    // Create user with guardian role if approved
    const userRole = status === 'approved' ? 'guardian' : 'user';
    const user = await this.createUser({
      name,
      email,
      password,
      role: userRole,
    });

    // Create guardian profile
    const guardian = await Guardian.create({
      user_id: user.id,
      designation,
      organization,
      bio: 'Test guardian bio',
      expertise_areas: ['test', 'expertise'],
      status,
      resources_count: 0,
      reports_count: 0,
    });

    return { user, guardian };
  }

  static async createIncident(reporterId?: string, guardianId?: string) {
    const { Incident } = require('../../models');
    
    return await Incident.create({
      title: 'Test Incident',
      description: 'Test incident description',
      platform: 'instagram',
      category: 'harassment',
      status: 'open',
      severity: 'medium',
      url: 'https://example.com/test',
      evidence_urls: [],
      is_anonymous: !reporterId,
      reporter_id: reporterId,
      reporter_contact: !reporterId ? '<EMAIL>' : null,
      guardian_id: guardianId,
      location: 'Test Location',
    });
  }

  static async createResource(authorId: string) {
    const { Resource } = require('../../models');
    
    return await Resource.create({
      title: 'Test Resource',
      description: 'Test resource description',
      content: 'Test resource content with enough characters to meet minimum requirements',
      category: 'safety',
      tags: ['test', 'resource'],
      attachment_urls: [],
      is_published: true,
      views_count: 0,
      likes_count: 0,
      author_id: authorId,
    });
  }
}

export const expectValidationError = (response: any, field?: string) => {
  expect(response.status).toBe(400);
  expect(response.body.success).toBe(false);
  if (field) {
    expect(response.body.message).toContain(field);
  }
};

export const expectUnauthorized = (response: any) => {
  expect(response.status).toBe(401);
  expect(response.body.success).toBe(false);
};

export const expectForbidden = (response: any) => {
  expect(response.status).toBe(403);
  expect(response.body.success).toBe(false);
};

export const expectNotFound = (response: any) => {
  expect(response.status).toBe(404);
  expect(response.body.success).toBe(false);
};

export const expectSuccess = (response: any, statusCode: number = 200) => {
  expect(response.status).toBe(statusCode);
  expect(response.body.success).toBe(true);
};
