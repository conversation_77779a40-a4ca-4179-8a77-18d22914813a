const { User, Role, Guardian } = require('../models');
const { generateTokenPair, verifyRefreshToken } = require('../utils/jwt');
const { CustomError, asyncHandler } = require('../middleware/errorHandler');

const register = asyncHandler(async (req, res) => {
  const { name, email, password } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({ where: { email } });
  if (existingUser) {
    throw new CustomError('User with this email already exists', 409);
  }

  // Get default user role
  const userRole = await Role.findOne({ where: { name: 'user' } });
  if (!userRole) {
    throw new CustomError('Default user role not found', 500);
  }

  // Create user
  const user = await User.create({
    name,
    email,
    password_hash: password, // Will be hashed by the model hook
    role_id: userRole.id,
    is_active: true,
    email_verified: false,
  });

  // Generate tokens
  const tokens = generateTokenPair({
    id: user.id,
    email: user.email,
    role: 'user',
    roleId: userRole.id,
  });

  const response = {
    success: true,
    message: 'User registered successfully',
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: 'user',
        isActive: user.is_active,
        emailVerified: user.email_verified,
        createdAt: user.created_at,
      },
      tokens,
    },
  };

  res.status(201).json(response);
});

const login = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Find user with role
  const user = await User.findOne({
    where: { email },
    include: [
      {
        model: Role,
        as: 'role',
        attributes: ['id', 'name'],
      },
    ],
  });

  if (!user) {
    throw new CustomError('Invalid email or password', 401);
  }

  if (!user.is_active) {
    throw new CustomError('Account is deactivated', 401);
  }

  // Validate password
  const isValidPassword = await user.validatePassword(password);
  if (!isValidPassword) {
    throw new CustomError('Invalid email or password', 401);
  }

  // Update last login
  await user.update({ last_login: new Date() });

  // Generate tokens
  const tokens = generateTokenPair({
    id: user.id,
    email: user.email,
    role: user.role?.name || 'user',
    roleId: user.role_id,
  });

  const response = {
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role?.name || 'user',
        isActive: user.is_active,
        emailVerified: user.email_verified,
        lastLogin: user.last_login,
        createdAt: user.created_at,
      },
      tokens,
    },
  };

  res.status(200).json(response);
});

const registerGuardian = asyncHandler(async (req, res) => {
  const { name, email, password, designation, organization, bio, expertiseAreas } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({ where: { email } });
  if (existingUser) {
    throw new CustomError('User with this email already exists', 409);
  }

  // Get default user role (guardian applications start as users)
  const userRole = await Role.findOne({ where: { name: 'user' } });
  if (!userRole) {
    throw new CustomError('Default user role not found', 500);
  }

  // Create user
  const user = await User.create({
    name,
    email,
    password_hash: password,
    role_id: userRole.id,
    is_active: true,
    email_verified: false,
  });

  // Create guardian application
  const guardian = await Guardian.create({
    user_id: user.id,
    designation,
    organization,
    bio,
    expertise_areas: expertiseAreas || [],
    status: 'pending',
    resources_count: 0,
    reports_count: 0,
  });

  const response = {
    success: true,
    message: 'Guardian application submitted successfully. Please wait for admin approval.',
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: 'user',
        isActive: user.is_active,
        emailVerified: user.email_verified,
        createdAt: user.created_at,
      },
      guardian: {
        id: guardian.id,
        designation: guardian.designation,
        organization: guardian.organization,
        status: guardian.status,
        createdAt: guardian.created_at,
      },
    },
  };

  res.status(201).json(response);
});

const refreshToken = asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  // Verify refresh token
  const decoded = verifyRefreshToken(refreshToken);

  // Find user
  const user = await User.findByPk(decoded.id, {
    include: [
      {
        model: Role,
        as: 'role',
        attributes: ['id', 'name'],
      },
    ],
  });

  if (!user || !user.is_active) {
    throw new CustomError('Invalid refresh token', 401);
  }

  // Generate new tokens
  const tokens = generateTokenPair({
    id: user.id,
    email: user.email,
    role: user.role?.name || 'user',
    roleId: user.role_id,
  });

  const response = {
    success: true,
    message: 'Token refreshed successfully',
    data: { tokens },
  };

  res.status(200).json(response);
});

const logout = asyncHandler(async (req, res) => {
  // In a real application, you might want to blacklist the token
  // For now, we'll just return a success response
  const response = {
    success: true,
    message: 'Logged out successfully',
  };

  res.status(200).json(response);
});

const getProfile = asyncHandler(async (req, res) => {
  const user = await User.findByPk(req.user.id, {
    include: [
      {
        model: Role,
        as: 'role',
        attributes: ['id', 'name'],
      },
      {
        model: Guardian,
        as: 'guardian',
        attributes: ['id', 'designation', 'organization', 'status', 'resources_count', 'reports_count'],
      },
    ],
  });

  if (!user) {
    throw new CustomError('User not found', 404);
  }

  const response = {
    success: true,
    data: {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role?.name || 'user',
        avatar: user.avatar,
        isActive: user.is_active,
        emailVerified: user.email_verified,
        lastLogin: user.last_login,
        createdAt: user.created_at,
        guardian: user.guardian,
      },
    },
  };

  res.status(200).json(response);
});

module.exports = {
  register,
  login,
  registerGuardian,
  refreshToken,
  logout,
  getProfile,
};
