'use client';

import { useState } from 'react';
import { getTypography, createButtonVariant, createCardVariant } from '@/lib/design-tokens';

export function PreferencesSettings() {
  const [preferences, setPreferences] = useState({
    language: 'en',
    timezone: 'Africa/Nairobi',
    theme: 'light',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    autoSave: true,
    compactView: false,
    showTips: true,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Preferences updated:', preferences);
  };

  return (
    <div className={`${createCardVariant('bordered')} p-6 space-y-6`}>
      <div>
        <h2 className={`${getTypography('title-b-18')} text-text-primary mb-2`}>
          Application Preferences
        </h2>
        <p className={`${getTypography('paragraph-r-14')} text-text-light`}>
          Customize your SafeHer experience with these preferences.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Language & Region */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Language & Region
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
                Language
              </label>
              <select
                value={preferences.language}
                onChange={(e) => setPreferences({ ...preferences, language: e.target.value })}
                className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="en">English</option>
                <option value="sw">Swahili</option>
                <option value="fr">French</option>
                <option value="ar">Arabic</option>
                <option value="es">Spanish</option>
              </select>
            </div>
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
                Timezone
              </label>
              <select
                value={preferences.timezone}
                onChange={(e) => setPreferences({ ...preferences, timezone: e.target.value })}
                className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="Africa/Nairobi">Africa/Nairobi (EAT)</option>
                <option value="Africa/Lagos">Africa/Lagos (WAT)</option>
                <option value="Africa/Cairo">Africa/Cairo (EET)</option>
                <option value="UTC">UTC</option>
                <option value="America/New_York">America/New_York (EST)</option>
              </select>
            </div>
          </div>
        </div>

        {/* Appearance */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Appearance
          </h3>
          <div>
            <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-3`}>
              Theme
            </label>
            <div className="space-y-2">
              {[
                { value: 'light', label: 'Light', description: 'Clean and bright interface' },
                { value: 'dark', label: 'Dark', description: 'Easy on the eyes in low light' },
                { value: 'auto', label: 'Auto', description: 'Matches your system preference' },
              ].map((option) => (
                <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="theme"
                    value={option.value}
                    checked={preferences.theme === option.value}
                    onChange={(e) => setPreferences({ ...preferences, theme: e.target.value })}
                    className="mt-1 h-4 w-4 text-primary focus:ring-primary border-border-dark"
                  />
                  <div>
                    <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                      {option.label}
                    </p>
                    <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                      {option.description}
                    </p>
                  </div>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* Date & Time Format */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Date & Time Format
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
                Date Format
              </label>
              <select
                value={preferences.dateFormat}
                onChange={(e) => setPreferences({ ...preferences, dateFormat: e.target.value })}
                className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                <option value="DD MMM YYYY">DD MMM YYYY</option>
              </select>
            </div>
            <div>
              <label className={`block ${getTypography('paragraph-m-12')} text-text-primary mb-2`}>
                Time Format
              </label>
              <select
                value={preferences.timeFormat}
                onChange={(e) => setPreferences({ ...preferences, timeFormat: e.target.value })}
                className="w-full px-3 py-2 border border-border-light rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="24h">24-hour (14:30)</option>
                <option value="12h">12-hour (2:30 PM)</option>
              </select>
            </div>
          </div>
        </div>

        {/* Interface Options */}
        <div className="space-y-4">
          <h3 className={`${getTypography('paragraph-b-14')} text-text-primary`}>
            Interface Options
          </h3>
          <div className="space-y-3">
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Auto-save drafts
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Automatically save your work as you type
                </p>
              </div>
              <input
                type="checkbox"
                checked={preferences.autoSave}
                onChange={(e) => setPreferences({ ...preferences, autoSave: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Compact view
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Show more content in less space
                </p>
              </div>
              <input
                type="checkbox"
                checked={preferences.compactView}
                onChange={(e) => setPreferences({ ...preferences, compactView: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
            <label className="flex items-center justify-between">
              <div>
                <p className={`${getTypography('paragraph-m-12')} text-text-primary`}>
                  Show helpful tips
                </p>
                <p className={`${getTypography('paragraph-r-10')} text-text-light`}>
                  Display tooltips and guidance throughout the app
                </p>
              </div>
              <input
                type="checkbox"
                checked={preferences.showTips}
                onChange={(e) => setPreferences({ ...preferences, showTips: e.target.checked })}
                className="h-4 w-4 text-primary focus:ring-primary border-border-dark rounded"
              />
            </label>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-border-light">
          <button
            type="button"
            className={`px-6 py-2 ${getTypography('paragraph-m-12')} text-text-light border border-border-light rounded-lg hover:bg-gray-50 transition-colors`}
          >
            Reset to Default
          </button>
          <button
            type="submit"
            className={`${createButtonVariant('primary')} ${getTypography('paragraph-m-12')}`}
          >
            Save Preferences
          </button>
        </div>
      </form>
    </div>
  );
}
