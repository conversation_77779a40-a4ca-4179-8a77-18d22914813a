const express = require('express');
const {
  createResource,
  getResources,
  getResourceById,
  updateResource,
  deleteResource,
  likeResource
} = require('../controllers/resourceController');
const { authenticate, optionalAuth, requireGuardianOrAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * /api/resources:
 *   post:
 *     summary: Create a new resource
 *     tags: [Resources]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - description
 *               - content
 *               - category
 *             properties:
 *               title:
 *                 type: string
 *                 minLength: 5
 *                 maxLength: 500
 *               description:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 1000
 *               content:
 *                 type: string
 *                 minLength: 50
 *                 maxLength: 50000
 *               category:
 *                 type: string
 *                 enum: [guide, article, video, tool, legal, support]
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               featured:
 *                 type: boolean
 *                 default: false
 *               published:
 *                 type: boolean
 *                 default: false
 *               downloadUrl:
 *                 type: string
 *                 format: uri
 *               externalUrl:
 *                 type: string
 *                 format: uri
 *     responses:
 *       201:
 *         description: Resource created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 */
router.post('/', authenticate, requireGuardianOrAdmin, createResource);

/**
 * @swagger
 * /api/resources:
 *   get:
 *     summary: Get resources list
 *     tags: [Resources]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *       - in: query
 *         name: featured
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: authorId
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: published
 *         schema:
 *           type: boolean
 *           default: true
 *     responses:
 *       200:
 *         description: Resources retrieved successfully
 */
router.get('/', optionalAuth, getResources);

/**
 * @swagger
 * /api/resources/{id}:
 *   get:
 *     summary: Get resource by ID
 *     tags: [Resources]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Resource retrieved successfully
 *       404:
 *         description: Resource not found
 */
router.get('/:id', optionalAuth, getResourceById);

/**
 * @swagger
 * /api/resources/{id}:
 *   put:
 *     summary: Update resource
 *     tags: [Resources]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               content:
 *                 type: string
 *               category:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               featured:
 *                 type: boolean
 *               published:
 *                 type: boolean
 *               downloadUrl:
 *                 type: string
 *                 format: uri
 *               externalUrl:
 *                 type: string
 *                 format: uri
 *     responses:
 *       200:
 *         description: Resource updated successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Resource not found
 */
router.put('/:id', authenticate, updateResource);

/**
 * @swagger
 * /api/resources/{id}:
 *   delete:
 *     summary: Delete resource
 *     tags: [Resources]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Resource deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Resource not found
 */
router.delete('/:id', authenticate, deleteResource);

/**
 * @swagger
 * /api/resources/{id}/like:
 *   post:
 *     summary: Like a resource
 *     tags: [Resources]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Resource liked successfully
 *       400:
 *         description: Cannot like unpublished resource
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Resource not found
 */
router.post('/:id/like', authenticate, likeResource);

module.exports = router;
