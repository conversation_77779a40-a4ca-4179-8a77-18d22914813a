# SafeHer Backend Development Guide

This guide covers development practices, code standards, and contribution guidelines for the SafeHer backend API.

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+ with npm
- MySQL 8.0+ (or Docker)
- Git
- Code editor (VS Code recommended)

### Quick Start

1. **Clone and install**
   ```bash
   git clone <repository-url>
   cd safeher-node
   npm install
   ```

2. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your local configuration
   ```

3. **Database setup**
   ```bash
   npm run db:create
   npm run db:migrate
   npm run db:seed
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

## 📁 Project Structure

```
src/
├── config/              # Configuration files
│   ├── database.ts      # Database connection config
│   └── sequelize.js     # Sequelize CLI config
├── controllers/         # Business logic controllers
│   ├── authController.ts
│   ├── userController.ts
│   ├── guardianController.ts
│   ├── incidentController.ts
│   └── resourceController.ts
├── middleware/          # Express middleware
│   ├── auth.ts          # Authentication middleware
│   ├── validation.ts    # Request validation
│   └── errorHandler.ts  # Global error handling
├── models/              # Sequelize models
│   ├── index.ts         # Model exports and associations
│   ├── Role.ts
│   ├── User.ts
│   ├── Guardian.ts
│   ├── Incident.ts
│   ├── Report.ts
│   └── Resource.ts
├── routes/              # API route definitions
│   ├── auth.ts
│   ├── users.ts
│   ├── guardians.ts
│   ├── incidents.ts
│   └── resources.ts
├── utils/               # Utility functions
│   └── jwt.ts           # JWT token utilities
├── validators/          # Zod validation schemas
│   └── auth.ts
├── migrations/          # Database migrations
├── seeders/             # Database seeders
├── tests/               # Test files
│   ├── helpers/         # Test utilities
│   ├── setup.ts         # Test configuration
│   ├── auth.test.ts
│   └── incidents.test.ts
└── index.ts             # Application entry point
```

## 🎯 Development Workflow

### Branch Strategy

- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/*` - Feature development branches
- `hotfix/*` - Critical bug fixes

### Commit Convention

Use conventional commits format:

```
type(scope): description

feat(auth): add guardian registration endpoint
fix(incidents): resolve status update validation
docs(readme): update installation instructions
test(auth): add login endpoint tests
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

### Pull Request Process

1. Create feature branch from `develop`
2. Implement changes with tests
3. Ensure all tests pass
4. Update documentation if needed
5. Create PR to `develop` branch
6. Code review and approval
7. Merge to `develop`

## 🧪 Testing Guidelines

### Test Structure

```
src/tests/
├── helpers/
│   └── testUtils.ts     # Test utilities and factories
├── setup.ts             # Global test setup
├── globalSetup.ts       # Jest global setup
├── globalTeardown.ts    # Jest global teardown
├── auth.test.ts         # Authentication tests
├── incidents.test.ts    # Incident management tests
└── resources.test.ts    # Resource management tests
```

### Writing Tests

1. **Use descriptive test names**
   ```typescript
   describe('POST /api/auth/register', () => {
     it('should register a new user successfully', async () => {
       // Test implementation
     });
     
     it('should fail with invalid email format', async () => {
       // Test implementation
     });
   });
   ```

2. **Use test factories**
   ```typescript
   import { TestDataFactory } from './helpers/testUtils';
   
   const user = await TestDataFactory.createUser({
     email: '<EMAIL>',
     role: 'guardian'
   });
   ```

3. **Test both success and failure cases**
   ```typescript
   // Success case
   expectSuccess(response, 201);
   expect(response.body.data.user).toHaveProperty('id');
   
   // Failure case
   expectValidationError(response, 'email');
   ```

### Running Tests

```bash
# Run all tests
npm test

# Run specific test file
npm test auth.test.ts

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## 🏗️ Code Standards

### TypeScript Guidelines

1. **Use strict TypeScript configuration**
   ```typescript
   // Enable strict mode in tsconfig.json
   "strict": true,
   "noImplicitAny": true,
   "strictNullChecks": true
   ```

2. **Define interfaces for data structures**
   ```typescript
   interface CreateUserRequest {
     name: string;
     email: string;
     password: string;
     confirmPassword: string;
   }
   ```

3. **Use proper error handling**
   ```typescript
   try {
     const user = await User.create(userData);
     return res.status(201).json({
       success: true,
       data: { user }
     });
   } catch (error) {
     next(error); // Let error middleware handle it
   }
   ```

### API Design Principles

1. **RESTful endpoints**
   ```
   GET    /api/resources           # List resources
   POST   /api/resources           # Create resource
   GET    /api/resources/:id       # Get specific resource
   PUT    /api/resources/:id       # Update resource
   DELETE /api/resources/:id       # Delete resource
   ```

2. **Consistent response format**
   ```typescript
   // Success response
   {
     success: true,
     data: { ... },
     message?: string
   }
   
   // Error response
   {
     success: false,
     message: string,
     errors?: ValidationError[]
   }
   ```

3. **Proper HTTP status codes**
   - 200: Success
   - 201: Created
   - 400: Bad Request (validation errors)
   - 401: Unauthorized
   - 403: Forbidden
   - 404: Not Found
   - 409: Conflict
   - 500: Internal Server Error

### Database Guidelines

1. **Model definitions**
   ```typescript
   @Table({
     tableName: 'users',
     timestamps: true,
     underscored: true
   })
   export class User extends Model {
     @Column({
       type: DataType.UUID,
       defaultValue: DataType.UUIDV4,
       primaryKey: true
     })
     id!: string;
   }
   ```

2. **Migration best practices**
   ```javascript
   // Always include both up and down methods
   module.exports = {
     async up(queryInterface, Sequelize) {
       await queryInterface.createTable('users', {
         // Column definitions
       });
     },
     
     async down(queryInterface, Sequelize) {
       await queryInterface.dropTable('users');
     }
   };
   ```

3. **Use transactions for complex operations**
   ```typescript
   const transaction = await sequelize.transaction();
   try {
     await User.create(userData, { transaction });
     await Guardian.create(guardianData, { transaction });
     await transaction.commit();
   } catch (error) {
     await transaction.rollback();
     throw error;
   }
   ```

## 🔧 Development Tools

### Recommended VS Code Extensions

- TypeScript Importer
- ESLint
- Prettier
- Thunder Client (API testing)
- MySQL (database management)
- GitLens

### Environment Configuration

```bash
# Development environment
NODE_ENV=development
DB_NAME=safeher_development
LOG_LEVEL=debug

# Test environment
NODE_ENV=test
DB_NAME=safeher_test
LOG_LEVEL=error
```

### Database Management

```bash
# Create new migration
npm run db:migration:create -- --name create-new-table

# Create new seeder
npm run db:seeder:create -- --name add-sample-data

# Reset database
npm run db:reset

# Check migration status
npm run db:migrate:status
```

## 🐛 Debugging

### Logging

```typescript
import { logger } from './utils/logger';

// Use appropriate log levels
logger.error('Critical error occurred', { error, userId });
logger.warn('Potential issue detected', { data });
logger.info('User action completed', { action, userId });
logger.debug('Debug information', { details });
```

### Database Debugging

```typescript
// Enable query logging in development
const sequelize = new Sequelize(config.database, {
  logging: process.env.NODE_ENV === 'development' ? console.log : false
});
```

### API Testing

Use Thunder Client or Postman for API testing:

1. Import the API collection
2. Set up environment variables
3. Test authentication flow
4. Verify all endpoints

## 📚 Documentation

### Code Documentation

```typescript
/**
 * Creates a new incident report
 * @param req - Express request object containing incident data
 * @param res - Express response object
 * @param next - Express next function for error handling
 * @returns Promise<void>
 */
export const createIncident = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  // Implementation
};
```

### API Documentation

- Use Swagger/OpenAPI annotations in route files
- Keep documentation up to date with code changes
- Include request/response examples

### README Updates

- Update README when adding new features
- Include setup instructions for new dependencies
- Document environment variables

## 🚀 Performance Considerations

### Database Optimization

1. **Add proper indexes**
   ```javascript
   // In migrations
   await queryInterface.addIndex('users', ['email']);
   await queryInterface.addIndex('incidents', ['status', 'created_at']);
   ```

2. **Use eager loading wisely**
   ```typescript
   // Good: Load related data when needed
   const incidents = await Incident.findAll({
     include: [{ model: User, as: 'reporter' }]
   });
   
   // Avoid: N+1 queries
   const incidents = await Incident.findAll();
   for (const incident of incidents) {
     const reporter = await incident.getReporter(); // N+1 query
   }
   ```

### API Performance

1. **Implement pagination**
   ```typescript
   const { page = 1, limit = 10 } = req.query;
   const offset = (page - 1) * limit;
   
   const { rows: incidents, count } = await Incident.findAndCountAll({
     limit: parseInt(limit),
     offset: parseInt(offset)
   });
   ```

2. **Use appropriate HTTP caching**
   ```typescript
   // Cache static resources
   res.set('Cache-Control', 'public, max-age=3600');
   ```

## 🔒 Security Best Practices

1. **Input validation**
   ```typescript
   // Always validate input with Zod schemas
   const createUserSchema = z.object({
     email: z.string().email(),
     password: z.string().min(8)
   });
   ```

2. **Authentication middleware**
   ```typescript
   // Protect routes that require authentication
   router.get('/profile', authenticate, getUserProfile);
   router.post('/admin-only', authenticate, requireAdmin, adminAction);
   ```

3. **Environment variables**
   ```bash
   # Never commit secrets to version control
   JWT_SECRET=your_secret_key
   DB_PASSWORD=your_db_password
   ```

## 🤝 Contributing

1. Follow the established code style
2. Write tests for new features
3. Update documentation
4. Use meaningful commit messages
5. Create detailed pull requests

For questions or support, reach out to the development team or create an issue in the repository.
