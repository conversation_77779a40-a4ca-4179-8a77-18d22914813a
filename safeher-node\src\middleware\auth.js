const { verifyAccessToken, extractTokenFromHeader } = require('../utils/jwt');
const { User, Role } = require('../models');
const { CustomError } = require('./errorHandler');

const authenticate = async (req, res, next) => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      throw new CustomError('Access token is required', 401);
    }

    const decoded = verifyAccessToken(token);
    
    // Verify user still exists and is active
    const user = await User.findByPk(decoded.id, {
      include: [
        {
          model: Role,
          as: 'role',
          attributes: ['id', 'name', 'permissions'],
        },
      ],
    });

    if (!user) {
      throw new CustomError('User not found', 401);
    }

    if (!user.is_active) {
      throw new CustomError('Account is deactivated', 401);
    }

    // Attach user info to request
    req.user = {
      id: user.id,
      email: user.email,
      role: user.role?.name || 'user',
      roleId: user.role_id,
    };

    next();
  } catch (error) {
    if (error instanceof CustomError) {
      next(error);
    } else {
      next(new CustomError('Invalid or expired token', 401));
    }
  }
};

const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new CustomError('Authentication required', 401));
    }

    if (!roles.includes(req.user.role)) {
      return next(new CustomError('Insufficient permissions', 403));
    }

    next();
  };
};

const optionalAuth = async (req, res, next) => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (token) {
      const decoded = verifyAccessToken(token);
      
      const user = await User.findByPk(decoded.id, {
        include: [
          {
            model: Role,
            as: 'role',
            attributes: ['id', 'name', 'permissions'],
          },
        ],
      });

      if (user && user.is_active) {
        req.user = {
          id: user.id,
          email: user.email,
          role: user.role?.name || 'user',
          roleId: user.role_id,
        };
      }
    }

    next();
  } catch (error) {
    // For optional auth, we don't throw errors, just continue without user
    next();
  }
};

const requireGuardianOrAdmin = (req, res, next) => {
  if (!req.user) {
    return next(new CustomError('Authentication required', 401));
  }

  if (!['guardian', 'admin'].includes(req.user.role)) {
    return next(new CustomError('Guardian or Admin access required', 403));
  }

  next();
};

const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return next(new CustomError('Authentication required', 401));
  }

  if (req.user.role !== 'admin') {
    return next(new CustomError('Admin access required', 403));
  }

  next();
};

const requireOwnershipOrAdmin = (resourceUserIdField = 'user_id') => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new CustomError('Authentication required', 401));
    }

    // Admin can access anything
    if (req.user.role === 'admin') {
      return next();
    }

    // Check if user owns the resource
    const resourceUserId = req.body[resourceUserIdField] || req.params[resourceUserIdField];
    
    if (resourceUserId && resourceUserId !== req.user.id) {
      return next(new CustomError('Access denied: You can only access your own resources', 403));
    }

    next();
  };
};

module.exports = {
  authenticate,
  authorize,
  optionalAuth,
  requireGuardianOrAdmin,
  requireAdmin,
  requireOwnershipOrAdmin,
};
