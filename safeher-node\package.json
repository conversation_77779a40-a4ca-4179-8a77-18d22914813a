{"name": "safeher-node", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "zod": "^3.25.71"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.0.10", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}