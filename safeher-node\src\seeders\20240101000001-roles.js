'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const roles = [
      {
        name: 'admin',
        description: 'System administrator with full access',
        permissions: JSON.stringify({
          users: ['create', 'read', 'update', 'delete'],
          guardians: ['create', 'read', 'update', 'delete', 'approve', 'reject', 'revoke'],
          incidents: ['create', 'read', 'update', 'delete', 'assign'],
          reports: ['create', 'read', 'update', 'delete'],
          resources: ['create', 'read', 'update', 'delete', 'publish'],
          dashboard: ['view_all_stats', 'export_data']
        }),
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'guardian',
        description: 'Verified expert who can moderate content and help users',
        permissions: JSON.stringify({
          users: ['read'],
          guardians: ['read'],
          incidents: ['create', 'read', 'update', 'assign'],
          reports: ['create', 'read', 'update'],
          resources: ['create', 'read', 'update', 'delete'],
          dashboard: ['view_assigned_stats']
        }),
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        name: 'user',
        description: 'Regular user who can report incidents and access resources',
        permissions: JSON.stringify({
          incidents: ['create', 'read_own'],
          reports: ['create', 'read_own'],
          resources: ['read'],
          profile: ['read', 'update']
        }),
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    await queryInterface.bulkInsert('roles', roles, {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('roles', null, {});
  }
};
