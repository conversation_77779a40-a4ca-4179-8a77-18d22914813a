'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

export default function IncidentsChart() {
  const chartData = [
    { day: 'Mon', facebook: 2000, instagram: 1500 },
    { day: 'Tue', facebook: 3000, instagram: 2500 },
    { day: 'Wed', facebook: 8000, instagram: 6000 },
    { day: 'Thu', facebook: 4000, instagram: 3500 },
    { day: 'Fri', facebook: 10000, instagram: 8500 },
    { day: 'Sat', facebook: 6000, instagram: 4500 },
    { day: 'Sun', facebook: 12000, instagram: 10000 }
  ];

  const maxValue = Math.max(...chartData.flatMap(d => [d.facebook, d.instagram]));

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-lg font-semibold">Incidences overtime</CardTitle>
          <p className="text-sm text-gray-600 mt-1">
            A simple display of incidences that have been reported to the community over time
          </p>
        </div>
        <button className="text-gray-400 hover:text-gray-600">
          <span className="text-xl">⋯</span>
        </button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Legend */}
          <div className="flex items-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-gray-600">Facebook</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
              <span className="text-gray-600">Instagram</span>
            </div>
          </div>

          {/* Chart */}
          <div className="relative h-64">
            {/* Y-axis labels */}
            <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500">
              <span>12K</span>
              <span>10K</span>
              <span>8K</span>
              <span>6K</span>
              <span>4K</span>
              <span>2K</span>
              <span>0</span>
            </div>

            {/* Chart area */}
            <div className="ml-8 h-full relative">
              <svg className="w-full h-full" viewBox="0 0 400 200">
                {/* Grid lines */}
                {[0, 1, 2, 3, 4, 5, 6].map((i) => (
                  <line
                    key={i}
                    x1="0"
                    y1={i * (200 / 6)}
                    x2="400"
                    y2={i * (200 / 6)}
                    stroke="#f3f4f6"
                    strokeWidth="1"
                  />
                ))}

                {/* Facebook line */}
                <polyline
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="2"
                  points={chartData.map((d, i) => 
                    `${(i * 400) / (chartData.length - 1)},${200 - (d.facebook / maxValue) * 200}`
                  ).join(' ')}
                />

                {/* Instagram line */}
                <polyline
                  fill="none"
                  stroke="#ec4899"
                  strokeWidth="2"
                  points={chartData.map((d, i) => 
                    `${(i * 400) / (chartData.length - 1)},${200 - (d.instagram / maxValue) * 200}`
                  ).join(' ')}
                />

                {/* Data points */}
                {chartData.map((d, i) => (
                  <g key={i}>
                    <circle
                      cx={(i * 400) / (chartData.length - 1)}
                      cy={200 - (d.facebook / maxValue) * 200}
                      r="4"
                      fill="#3b82f6"
                    />
                    <circle
                      cx={(i * 400) / (chartData.length - 1)}
                      cy={200 - (d.instagram / maxValue) * 200}
                      r="4"
                      fill="#ec4899"
                    />
                  </g>
                ))}
              </svg>
            </div>

            {/* X-axis labels */}
            <div className="absolute bottom-0 left-8 right-0 flex justify-between text-xs text-gray-500 mt-2">
              {chartData.map((d) => (
                <span key={d.day}>{d.day}</span>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
