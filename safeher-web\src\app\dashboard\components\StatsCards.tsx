import { Card, CardContent } from '@/components/ui/Card';
import { formatNumber } from '@/lib/utils';

const stats = [
  {
    title: 'Total Reported Incidences',
    value: 200000,
    icon: '🏠',
    bgColor: 'bg-blue-50',
    iconColor: 'text-blue-600'
  },
  {
    title: 'Total Guardians',
    value: 40000,
    icon: '👥',
    bgColor: 'bg-purple-50',
    iconColor: 'text-purple-600'
  },
  {
    title: 'Total Guardians',
    value: 2000,
    icon: '👤',
    bgColor: 'bg-green-50',
    iconColor: 'text-green-600'
  },
  {
    title: 'Total Users',
    value: 2000000,
    icon: '👥',
    bgColor: 'bg-orange-50',
    iconColor: 'text-orange-600'
  }
];

export default function StatsCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <Card key={index} className="border-0 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                <span className={`text-2xl ${stat.iconColor}`}>
                  {stat.icon}
                </span>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(stat.value)}
                </p>
                <p className="text-sm text-gray-600">
                  {stat.title}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
