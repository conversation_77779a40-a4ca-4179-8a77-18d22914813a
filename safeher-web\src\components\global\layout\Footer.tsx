import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-white border-t border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-2">
            <Link href="/" className="text-2xl font-bold text-pink-600 mb-4 block">
              SafeHer
            </Link>
            <p className="text-gray-600 text-sm max-w-md">
              SafeHer by SafeOnline Women Kenya is a pioneering mobile app specifically 
              designed to combat online violence against women. With its innovative 
              features, community-driven reporting system, and commitment to education, 
              SafeHer empowers women to navigate the online landscape with confidence, 
              fostering a safer and more inclusive digital environment.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/landing" className="text-sm text-gray-600 hover:text-pink-600">
                  For Her
                </Link>
              </li>
              <li>
                <Link href="/for-guardians" className="text-sm text-gray-600 hover:text-pink-600">
                  For Guardians
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-sm text-gray-600 hover:text-pink-600">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-sm text-gray-600 hover:text-pink-600">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Download Links */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 mb-4">Download App</h3>
            <div className="space-y-3">
              <a
                href="#"
                className="flex items-center space-x-2 text-sm text-gray-600 hover:text-pink-600"
              >
                <span>📱</span>
                <span>Download Play store</span>
              </a>
              <a
                href="#"
                className="flex items-center space-x-2 text-sm text-gray-600 hover:text-pink-600"
              >
                <span>🍎</span>
                <span>Download App store</span>
              </a>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              © 2024 SafeHer All Rights Reserved
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/terms" className="text-sm text-gray-600 hover:text-pink-600">
                Terms and conditions
              </Link>
              <Link href="/privacy" className="text-sm text-gray-600 hover:text-pink-600">
                Privacy Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
