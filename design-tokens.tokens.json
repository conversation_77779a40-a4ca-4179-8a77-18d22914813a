{"gradient": {"linear-one": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 135, "stops": [{"position": 0, "color": "#cf188400"}, {"position": 1, "color": "#cf18844a"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:dfecc07ce8a91d0344608d8d993c8749582c8ecd,", "exportKey": "gradient"}}}, "linear-mix": {"description": "", "type": "custom-gradient", "value": {"gradientType": "linear", "rotation": 180, "stops": [{"position": 0, "color": "#5b8cdb3a"}, {"position": 1, "color": "#fe83c81a"}]}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:465f3aceb67cc5f3bb82410ade5a1f66ab84ca94,", "exportKey": "gradient"}}}}, "font": {"title-bold-64": {"type": "custom-fontStyle", "value": {"fontSize": 64, "textDecoration": "none", "fontFamily": "Source Sans Pro", "fontWeight": 700, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 76.8, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:627d1883e57a50dad3757e7c9a9812781ab8f8a2,", "exportKey": "font"}}}, "lead-r-27": {"type": "custom-fontStyle", "value": {"fontSize": 27, "textDecoration": "none", "fontFamily": "Source Sans Pro", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 37.8, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:d2531a4977da7517139485bac7af0fed40a91119,", "exportKey": "font"}}}, "paragraph-r-20": {"type": "custom-fontStyle", "value": {"fontSize": 20, "textDecoration": "none", "fontFamily": "Nunito", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 32, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:0c682426f8866952bbbb017589d7120427468eb0,", "exportKey": "font"}}}, "link-r-20": {"type": "custom-fontStyle", "value": {"fontSize": 20, "textDecoration": "none", "fontFamily": "Montserrat", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 32, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:7ca325ee021df197c1acc7332c80cd7eab89fee2,", "exportKey": "font"}}}, "button-r-20": {"type": "custom-fontStyle", "value": {"fontSize": 20, "textDecoration": "none", "fontFamily": "Montserrat", "fontWeight": 700, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 32, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:1794dded730082a0d730a3d4e0ecc2578998173b,", "exportKey": "font"}}}, "lead-b-12": {"type": "custom-fontStyle", "value": {"fontSize": 14, "textDecoration": "none", "fontFamily": "Source Sans Pro", "fontWeight": 700, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 19.6, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:a7b4358e76a857c9afede19462bb8b85c93bf6c8,", "exportKey": "font"}}}, "paragraph-m-10": {"type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Nunito", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 19.2, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:f1d07bed01eeff9f0ff7a22d7101b4e9fd988a08,", "exportKey": "font"}}}, "paragraph-r-12": {"type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Nunito", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 14.4, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:ba36d7397a1195a36250380849994c187fe9827e,", "exportKey": "font"}}}, "lead-r-10": {"type": "custom-fontStyle", "value": {"fontSize": 10, "textDecoration": "none", "fontFamily": "Source Sans Pro", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 14, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:3a9a43a47dd336e79f8f92ce249f7728e41b72c7,", "exportKey": "font"}}}, "paragraph-r-10": {"type": "custom-fontStyle", "value": {"fontSize": 10, "textDecoration": "none", "fontFamily": "Nunito", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 12, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:5b461ef39703dc864d48dac7b7a96fa96a2bb388,", "exportKey": "font"}}}, "paragraph-m-12": {"type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Nunito", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 14.4, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:6eac399998d19a76465ccbbd04a4080a16b55274,", "exportKey": "font"}}}, "title-b-16": {"type": "custom-fontStyle", "value": {"fontSize": 18, "textDecoration": "none", "fontFamily": "Source Sans Pro", "fontWeight": 700, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 21.6, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:de22cd1cb2be6a298392298d44fb6d45de5c9b1b,", "exportKey": "font"}}}, "paragraph-m-16": {"type": "custom-fontStyle", "value": {"fontSize": 16, "textDecoration": "none", "fontFamily": "Nunito", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 25.6, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:8951784ebcb83d475a4d43357d2682c40b036933,", "exportKey": "font"}}}, "lead-sm-10": {"type": "custom-fontStyle", "value": {"fontSize": 10, "textDecoration": "none", "fontFamily": "Source Sans Pro", "fontWeight": 600, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 14, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:17976254c4065b95fa7c9ee28b5c04bc29a84314,", "exportKey": "font"}}}, "title-b-14": {"type": "custom-fontStyle", "value": {"fontSize": 14, "textDecoration": "none", "fontFamily": "Source Sans Pro", "fontWeight": 700, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 16.8, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:2f08ac15720f9e5cb45026df998515553f4b145e,", "exportKey": "font"}}}, "lead-sm-16": {"type": "custom-fontStyle", "value": {"fontSize": 16, "textDecoration": "none", "fontFamily": "Source Sans Pro", "fontWeight": 600, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 22.4, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:bb230cf0af40db1a78d55c5b1609acffb36b7556,", "exportKey": "font"}}}, "lead-sm-20": {"type": "custom-fontStyle", "value": {"fontSize": 20, "textDecoration": "none", "fontFamily": "Source Sans Pro", "fontWeight": 600, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 28, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:3a831297fa370fd1931a8fa4dace0248fda3136c,", "exportKey": "font"}}}, "title-b-20": {"type": "custom-fontStyle", "value": {"fontSize": 20, "textDecoration": "none", "fontFamily": "Source Sans Pro", "fontWeight": 700, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 24, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:d62d6712730de0ddee805b8cdaeb262282adce67,", "exportKey": "font"}}}, "link-r-16": {"type": "custom-fontStyle", "value": {"fontSize": 16, "textDecoration": "none", "fontFamily": "Montserrat", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 25.6, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:31adf96d0b763f6edd7901ad0413592c3539548a,", "exportKey": "font"}}}, "lead-r-14": {"type": "custom-fontStyle", "value": {"fontSize": 14, "textDecoration": "none", "fontFamily": "Source Sans Pro", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 19.6, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:ae3c4357d4e731d204922bf8fc0d83829a9bbf4b,", "exportKey": "font"}}}, "link-r-12": {"type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Montserrat", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 19.2, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:c03eb99b45d257ceb8ce66a26a5f94a5b5193296,", "exportKey": "font"}}}}, "effect": {"my drop shadow": {"description": null, "type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 10, "color": "#d0d5dd40", "offsetX": 0, "offsetY": 4, "spread": 0}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:749bfe74ecb01ba524f8d73da801ca182fd703a2,", "exportKey": "effect"}}}, "presention-shadow": {"description": null, "type": "custom-shadow", "value": {"shadowType": "dropShadow", "radius": 38, "color": "#0000002b", "offsetX": 0, "offsetY": 0, "spread": 17}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:2a49de9c611f9ae12bbb60c2cc00d7a13b285a6f,", "exportKey": "effect"}}}}, "collection 1": {"primary color": {"type": "color", "value": "#cf1884ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8462", "exportKey": "variables"}}}, "secondary color": {"type": "color", "value": "#3364b3ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8463", "exportKey": "variables"}}}, "primary white": {"type": "color", "value": "#ffffffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8464", "exportKey": "variables"}}}, "primary shade": {"type": "color", "value": "#f651adff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8465", "exportKey": "variables"}}}, "secondary shade": {"type": "color", "value": "#5f8edbff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8466", "exportKey": "variables"}}}, "primary light shade": {"type": "color", "value": "#fe83c8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8467", "exportKey": "variables"}}}, "secondary light shade": {"type": "color", "value": "#98b0d6ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8468", "exportKey": "variables"}}}, "border dark": {"type": "color", "value": "#d0d5ddff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8469", "exportKey": "variables"}}}, "border light": {"type": "color", "value": "#eaecf0ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8470", "exportKey": "variables"}}}, "primary text": {"type": "color", "value": "#2b1a55ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8471", "exportKey": "variables"}}}, "primary dark text": {"type": "color", "value": "#48396cff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8472", "exportKey": "variables"}}}, "primary light text": {"type": "color", "value": "#706390ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8473", "exportKey": "variables"}}}, "primary lighter text": {"type": "color", "value": "#c0b9d1ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "Collection 1", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:3:8474", "exportKey": "variables"}}}}, "typography": {"title-bold-64": {"fontSize": {"type": "dimension", "value": 64}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Source Sans Pro"}, "fontWeight": {"type": "number", "value": 700}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 76.8}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "lead-r-27": {"fontSize": {"type": "dimension", "value": 27}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Source Sans Pro"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 37.8}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "paragraph-r-20": {"fontSize": {"type": "dimension", "value": 20}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Nunito"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 32}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "link-r-20": {"fontSize": {"type": "dimension", "value": 20}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Montserrat"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 32}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "button-r-20": {"fontSize": {"type": "dimension", "value": 20}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Montserrat"}, "fontWeight": {"type": "number", "value": 700}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 32}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "lead-b-12": {"fontSize": {"type": "dimension", "value": 14}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Source Sans Pro"}, "fontWeight": {"type": "number", "value": 700}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 19.6}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "paragraph-m-10": {"fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Nunito"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 19.2}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "paragraph-r-12": {"fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Nunito"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 14.4}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "lead-r-10": {"fontSize": {"type": "dimension", "value": 10}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Source Sans Pro"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 14}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "paragraph-r-10": {"fontSize": {"type": "dimension", "value": 10}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Nunito"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 12}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "paragraph-m-12": {"fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Nunito"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 14.4}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "title-b-16": {"fontSize": {"type": "dimension", "value": 18}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Source Sans Pro"}, "fontWeight": {"type": "number", "value": 700}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 21.6}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "paragraph-m-16": {"fontSize": {"type": "dimension", "value": 16}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Nunito"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 25.6}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "lead-sm-10": {"fontSize": {"type": "dimension", "value": 10}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Source Sans Pro"}, "fontWeight": {"type": "number", "value": 600}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 14}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "title-b-14": {"fontSize": {"type": "dimension", "value": 14}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Source Sans Pro"}, "fontWeight": {"type": "number", "value": 700}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 16.8}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "lead-sm-16": {"fontSize": {"type": "dimension", "value": 16}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Source Sans Pro"}, "fontWeight": {"type": "number", "value": 600}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 22.4}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "lead-sm-20": {"fontSize": {"type": "dimension", "value": 20}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Source Sans Pro"}, "fontWeight": {"type": "number", "value": 600}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 28}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "title-b-20": {"fontSize": {"type": "dimension", "value": 20}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Source Sans Pro"}, "fontWeight": {"type": "number", "value": 700}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 24}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "link-r-16": {"fontSize": {"type": "dimension", "value": 16}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Montserrat"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 25.6}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "lead-r-14": {"fontSize": {"type": "dimension", "value": 14}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Source Sans Pro"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 19.6}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "link-r-12": {"fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Montserrat"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 19.2}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}}