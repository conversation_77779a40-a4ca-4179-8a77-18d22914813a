# SafeHer Backend API (JavaScript)

SafeHer is a comprehensive platform designed to combat online violence against women. This repository contains the backend API built with Node.js, Express.js, and MySQL.

## 🚀 Features

- **User Management**: Registration, authentication, and profile management
- **Role-based Access Control**: Admin, Guardian, and User roles with specific permissions
- **Incident Reporting**: Anonymous and authenticated incident reporting system
- **Guardian System**: Expert approval workflow for content moderation
- **Resource Management**: Educational content creation and publishing
- **Report Generation**: Follow-up reporting on incidents
- **JWT Authentication**: Secure token-based authentication
- **API Documentation**: Comprehensive Swagger/OpenAPI documentation

## 🛠️ Tech Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: JavaScript (ES6+)
- **Database**: MySQL with Sequelize ORM
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: Zod
- **Documentation**: Swagger/OpenAPI
- **Security**: Helmet, CORS, Rate Limiting

## 📋 Prerequisites

- Node.js 18 or higher
- MySQL 8.0 or higher
- npm package manager

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd safeher-node
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   # Database Configuration
   DB_HOST=localhost
   DB_PORT=3306
   DB_NAME=safeher_db
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   
   # JWT Configuration
   JWT_SECRET=your_super_secret_jwt_key
   JWT_REFRESH_SECRET=your_super_secret_refresh_key
   
   # Other configurations...
   ```

4. **Set up the database**
   ```bash
   # Create database
   npm run db:create
   
   # Run migrations
   npm run db:migrate
   
   # Seed initial data
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

The API will be available at `http://localhost:3001`

## 📚 API Documentation

Once the server is running, you can access the interactive API documentation at:
- **Swagger UI**: `http://localhost:3001/api/docs`
- **Health Check**: `http://localhost:3001/health`

## 🗂️ Project Structure

```
src/
├── config/          # Configuration files
│   ├── database.js  # Database configuration
│   └── config.js    # Sequelize CLI configuration
├── controllers/     # Business logic controllers
│   └── authController.js
├── middleware/      # Express middleware
│   ├── auth.js      # Authentication middleware
│   ├── validation.js # Validation middleware
│   └── errorHandler.js
├── models/          # Sequelize models
│   ├── index.js
│   ├── Role.js
│   ├── User.js
│   ├── Guardian.js
│   ├── Incident.js
│   ├── Report.js
│   └── Resource.js
├── routes/          # API routes
│   └── auth.js
├── utils/           # Utility functions
│   └── jwt.js
├── validators/      # Zod validation schemas
│   └── auth.js
├── migrations/      # Database migrations
├── seeders/         # Database seeders
└── index.js         # Application entry point
```

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Default Admin Account

After running the seeders, a default admin account is created:
- **Email**: `<EMAIL>`
- **Password**: `SafeHer@Admin123!`

⚠️ **Important**: Change the default password after first login!

## 📝 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/register-guardian` - Guardian application
- `GET /api/auth/profile` - Get user profile
- `POST /api/auth/logout` - User logout
- `POST /api/auth/refresh` - Refresh JWT token

For detailed API documentation with request/response schemas, visit the Swagger UI at `/api/docs` when the server is running.

## 🚀 Available Scripts

```bash
# Start production server
npm start

# Start development server with auto-reload
npm run dev

# Database operations
npm run db:create     # Create database
npm run db:migrate    # Run migrations
npm run db:seed       # Run seeders
npm run db:reset      # Reset database (drop, create, migrate, seed)

# Code quality
npm run lint          # Run ESLint
npm run lint:fix      # Fix ESLint issues
```

## 🛡️ Security Features

- **Password Hashing**: bcrypt with salt rounds
- **JWT Tokens**: Access and refresh token system
- **Rate Limiting**: API rate limiting to prevent abuse
- **CORS**: Cross-origin resource sharing configuration
- **Helmet**: Security headers middleware
- **Input Validation**: Comprehensive request validation with Zod
- **SQL Injection Protection**: Sequelize ORM with parameterized queries

## 🚀 Deployment

### Environment Variables

Ensure all required environment variables are set:

```env
NODE_ENV=production
DB_HOST=your_production_db_host
DB_NAME=your_production_db_name
DB_USER=your_production_db_user
DB_PASSWORD=your_production_db_password
JWT_SECRET=your_production_jwt_secret
JWT_REFRESH_SECRET=your_production_refresh_secret
```

### Production Build

```bash
# Start production server
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions, please contact the development team or create an issue in the repository.

---

**SafeHer** - Empowering women's safety in the digital world 🛡️
